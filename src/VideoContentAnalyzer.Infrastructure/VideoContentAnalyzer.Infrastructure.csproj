<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Google.Apis.YouTube.v3" Version="1.70.0.3847" />
    <PackageReference Include="RestSharp" Version="112.0.0" />
    <PackageReference Include="FFMpegCore" Version="5.0.2" />
    <PackageReference Include="System.IO.Abstractions" Version="21.1.3" />
    <PackageReference Include="Whisper.net" Version="1.7.1" />
    <PackageReference Include="SubtitlesParser" Version="1.5.1" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.8" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.0" />
    <PackageReference Include="Whisper.net.Runtime" Version="1.8.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="../VideoContentAnalyzer.Core/VideoContentAnalyzer.Core.csproj" />
  </ItemGroup>

</Project>