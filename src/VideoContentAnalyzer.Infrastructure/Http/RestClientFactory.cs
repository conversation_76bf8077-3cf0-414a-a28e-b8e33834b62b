using RestSharp;

namespace VideoContentAnalyzer.Infrastructure.Http;

/// <summary>
/// RestClient 工廠實現，用於創建不同配置的 RestClient 實例
/// </summary>
public class RestClientFactory : IRestClientFactory
{
    /// <summary>
    /// 創建 RestClient 實例
    /// </summary>
    /// <param name="options">RestClient 選項</param>
    /// <returns>IRestClient 實例</returns>
    public IRestClient CreateClient(RestClientOptions options)
    {
        return new RestClientWrapper(options);
    }
}
