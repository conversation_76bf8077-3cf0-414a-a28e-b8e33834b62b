using RestSharp;

namespace VideoContentAnalyzer.Infrastructure.Http;

/// <summary>
/// RestClient 的包裝器實現，用於支持依賴注入
/// </summary>
public class RestClientWrapper : IRestClient
{
    private readonly RestClient _client;
    private bool _disposed = false;

    /// <summary>
    /// 初始化 RestClientWrapper
    /// </summary>
    /// <param name="options">RestClient 選項</param>
    public RestClientWrapper(RestClientOptions options)
    {
        _client = new RestClient(options);
    }

    /// <summary>
    /// 執行 REST 請求
    /// </summary>
    /// <param name="request">REST 請求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>REST 回應</returns>
    public Task<RestResponse> ExecuteAsync(RestRequest request, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(RestClientWrapper));
        return _client.ExecuteAsync(request, cancellationToken);
    }

    /// <summary>
    /// 釋放資源
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 釋放資源的實際實現
    /// </summary>
    /// <param name="disposing">是否正在釋放</param>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            _client?.Dispose();
            _disposed = true;
        }
    }
}
