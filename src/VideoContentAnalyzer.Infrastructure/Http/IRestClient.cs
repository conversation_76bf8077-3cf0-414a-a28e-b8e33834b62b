using RestSharp;

namespace VideoContentAnalyzer.Infrastructure.Http;

/// <summary>
/// RestClient 的抽象接口，用於支持依賴注入和單元測試
/// </summary>
public interface IRestClient : IDisposable
{
    /// <summary>
    /// 執行 REST 請求
    /// </summary>
    /// <param name="request">REST 請求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>REST 回應</returns>
    Task<RestResponse> ExecuteAsync(RestRequest request, CancellationToken cancellationToken = default);
}
