<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <OutputType>Exe</OutputType>
    <StartupObject>VideoContentAnalyzer.Tests.IntegrationTestProgram</StartupObject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="xunit" Version="2.4.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.4.5" />
    <PackageReference Include="Moq" Version="4.20.69" />
    <PackageReference Include="FluentAssertions" Version="6.12.0" />
    <PackageReference Include="Microsoft.Extensions.FileSystemGlobbing" Version="8.0.0" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="System.IO.Abstractions" Version="19.2.87" />
    <PackageReference Include="System.IO.Abstractions.TestingHelpers" Version="19.2.87" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="../src/VideoContentAnalyzer.Core/VideoContentAnalyzer.Core.csproj" />
    <ProjectReference Include="../src/VideoContentAnalyzer.Infrastructure/VideoContentAnalyzer.Infrastructure.csproj" />
  </ItemGroup>

</Project>
