// using Microsoft.Extensions.Logging;
// using Microsoft.Extensions.Options;
// using NSubstitute;
// using RestSharp;
// using System.IO.Abstractions;
// using System.IO.Abstractions.TestingHelpers;
// using VideoContentAnalyzer.Core.Interfaces;
// using VideoContentAnalyzer.Core.Models;
// using VideoContentAnalyzer.Infrastructure.AI;
// using VideoContentAnalyzer.Infrastructure.Http;
//
// namespace VideoContentAnalyzer.UnitTests.TestHelpers;
//
// /// <summary>
// /// 測試固定裝置，提供常用的模擬物件和設定
// /// </summary>
// public class TestFixtures
// {
//     /// <summary>
//     /// 建立模擬的檔案系統
//     /// </summary>
//     public static MockFileSystem CreateMockFileSystem()
//     {
//         var fileSystem = new MockFileSystem();
//
//         // 建立測試用的檔案和目錄
//         fileSystem.AddDirectory("/test");
//         fileSystem.AddDirectory("/test/frames");
//         fileSystem.AddDirectory("/test/output");
//
//         // 建立測試用的圖片檔案
//         fileSystem.AddFile("/test/frame1.jpg", new MockFileData("fake image data"));
//         fileSystem.AddFile("/test/frame2.jpg", new MockFileData("fake image data"));
//
//         // 建立測試用的影片檔案
//         fileSystem.AddFile("/test/video.mp4", new MockFileData("fake video data"));
//
//         // 建立測試用的字幕檔案
//         fileSystem.AddFile("/test/subtitle.srt", new MockFileData(CreateSampleSrtContent()));
//         fileSystem.AddFile("/test/subtitle.vtt", new MockFileData(CreateSampleVttContent()));
//
//         return fileSystem;
//     }
//
//     /// <summary>
//     /// 建立模擬的 LMStudioOptions
//     /// </summary>
//     public static IOptions<LMStudioOptions> CreateMockLMStudioOptions()
//     {
//         var options = MockData.CreateLMStudioOptions();
//         return Options.Create(options);
//     }
//
//     /// <summary>
//     /// 建立模擬的 WhisperOptions
//     /// </summary>
//     public static IOptions<WhisperOptions> CreateMockWhisperOptions()
//     {
//         var options = new WhisperOptions
//         {
//             ModelPath = "/test/whisper-model",
//             Language = "auto",
//             ModelSize = "base"
//         };
//         return Options.Create(options);
//     }
//
//     /// <summary>
//     /// 建立模擬的 FrameExtractionOptions
//     /// </summary>
//     public static IOptions<FrameExtractionOptions> CreateMockFrameExtractionOptions()
//     {
//         var options = new FrameExtractionOptions
//         {
//             OutputDirectory = "/test/frames",
//             UseTimestampInFilename = true,
//             ImageFormat = "jpg",
//             ImageQuality = 85,
//             KeepFrames = false,
//             CreateSubDirectoryPerVideo = true
//         };
//         return Options.Create(options);
//     }
//
//     /// <summary>
//     /// 建立模擬的 Logger
//     /// </summary>
//     public static ILogger<T> CreateMockLogger<T>()
//     {
//         return Substitute.For<ILogger<T>>();
//     }
//
//     /// <summary>
//     /// 建立模擬的 IRestClient
//     /// </summary>
//     public static VideoContentAnalyzer.Infrastructure.Http.IRestClient CreateMockRestClient()
//     {
//         var mockRestClient = Substitute.For<VideoContentAnalyzer.Infrastructure.Http.IRestClient>();
//
//         // 創建一個成功的 RestResponse
//         var successResponse = CreateMockRestResponse(true, System.Net.HttpStatusCode.OK, """
//             {
//                 "choices": [
//                     {
//                         "message": {
//                             "content": "這是一個測試回應"
//                         }
//                     }
//                 ]
//             }
//             """);
//
//         mockRestClient.ExecuteAsync(Arg.Any<RestRequest>(), Arg.Any<CancellationToken>())
//             .Returns(Task.FromResult(successResponse));
//
//         return mockRestClient;
//     }
//
//     /// <summary>
//     /// 建立模擬的 IRestClientFactory
//     /// </summary>
//     public static IRestClientFactory CreateMockRestClientFactory()
//     {
//         var mockFactory = Substitute.For<IRestClientFactory>();
//         var mockRestClient = CreateMockRestClient();
//
//         mockFactory.CreateClient(Arg.Any<RestClientOptions>())
//             .Returns(mockRestClient);
//
//         return mockFactory;
//     }
//
//     /// <summary>
//     /// 建立模擬的 IRestClient，並設定特定的回應內容
//     /// </summary>
//     public static VideoContentAnalyzer.Infrastructure.Http.IRestClient CreateMockRestClientWithResponse(string responseContent, bool isSuccessful = true)
//     {
//         var mockRestClient = Substitute.For<VideoContentAnalyzer.Infrastructure.Http.IRestClient>();
//
//         var response = CreateMockRestResponse(isSuccessful,
//             isSuccessful ? System.Net.HttpStatusCode.OK : System.Net.HttpStatusCode.BadRequest,
//             responseContent);
//
//         mockRestClient.ExecuteAsync(Arg.Any<RestRequest>(), Arg.Any<CancellationToken>())
//             .Returns(Task.FromResult(response));
//
//         return mockRestClient;
//     }
//
//     /// <summary>
//     /// 創建模擬的 RestResponse
//     /// </summary>
//     private static RestResponse CreateMockRestResponse(bool isSuccessful, System.Net.HttpStatusCode statusCode, string content)
//     {
//         var response = Substitute.For<RestResponse>();
//         response.IsSuccessful.Returns(isSuccessful);
//         response.StatusCode.Returns(statusCode);
//         response.Content.Returns(content);
//         response.ErrorMessage.Returns(isSuccessful ? null : "Mock error message");
//         return response;
//     }
//
//     /// <summary>
//     /// 建立範例 SRT 字幕內容
//     /// </summary>
//     private static string CreateSampleSrtContent()
//     {
//         return """
//         1
//         00:00:10,000 --> 00:00:15,000
//         今天我們來到了東京最有名的拉麵店
//
//         2
//         00:00:15,500 --> 00:00:20,000
//         這家店的豚骨拉麵非常有名
//
//         3
//         00:00:20,500 --> 00:00:25,000
//         讓我們一起來品嚐看看
//         """;
//     }
//
//     /// <summary>
//     /// 建立範例 VTT 字幕內容
//     /// </summary>
//     private static string CreateSampleVttContent()
//     {
//         return """
//         WEBVTT
//
//         00:00:10.000 --> 00:00:15.000
//         今天我們來到了東京最有名的拉麵店
//
//         00:00:15.500 --> 00:00:20.000
//         這家店的豚骨拉麵非常有名
//
//         00:00:20.500 --> 00:00:25.000
//         讓我們一起來品嚐看看
//         """;
//     }
//
//     /// <summary>
//     /// 建立測試用的 CancellationToken
//     /// </summary>
//     public static CancellationToken CreateTimeoutToken(int timeoutMs = 5000)
//     {
//         var cts = new CancellationTokenSource(timeoutMs);
//         return cts.Token;
//     }
//
//     /// <summary>
//     /// 建立已取消的 CancellationToken
//     /// </summary>
//     public static CancellationToken CreateCancelledToken()
//     {
//         var cts = new CancellationTokenSource();
//         cts.Cancel();
//         return cts.Token;
//     }
//
//     /// <summary>
//     /// 驗證 Logger 是否記錄了特定等級的日誌
//     /// </summary>
//     public static void VerifyLoggerCalled<T>(ILogger<T> mockLogger, LogLevel logLevel, int expectedCallCount = 1)
//     {
//         mockLogger.Received(expectedCallCount).Log(
//             logLevel,
//             Arg.Any<EventId>(),
//             Arg.Any<object>(),
//             Arg.Any<Exception>(),
//             Arg.Any<Func<object, Exception?, string>>());
//     }
//
//     /// <summary>
//     /// 驗證 Logger 是否記錄了包含特定訊息的日誌
//     /// </summary>
//     public static void VerifyLoggerCalledWithMessage<T>(ILogger<T> mockLogger, LogLevel logLevel, string message)
//     {
//         mockLogger.Received().Log(
//             logLevel,
//             Arg.Any<EventId>(),
//             Arg.Is<object>(v => v.ToString()!.Contains(message)),
//             Arg.Any<Exception>(),
//             Arg.Any<Func<object, Exception?, string>>());
//     }
// }
