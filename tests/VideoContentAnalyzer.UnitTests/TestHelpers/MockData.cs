using VideoContentAnalyzer.Core.Models;
using VideoContentAnalyzer.Infrastructure.AI;

namespace VideoContentAnalyzer.UnitTests.TestHelpers;

/// <summary>
/// 提供測試用的模擬資料
/// </summary>
public static class MockData
{
    /// <summary>
    /// 建立測試用的 SceneDescription
    /// </summary>
    public static SceneDescription CreateSceneDescription()
    {
        return new SceneDescription
        {
            MainDescription = "一家日式拉麵店的外觀，招牌上寫著店名",
            CuisineType = "日式拉麵",
            RestaurantCategory = "拉麵店",
            VisibleTexts = new List<string> { "一蘭拉麵", "新宿店", "營業中" },
            Activities = new List<string> { "用餐", "排隊" },
            Setting = "位於東京新宿區的繁華街道",
            Colors = new List<string> { "紅色", "白色", "黑色" },
            Mood = "熱鬧"
        };
    }

    /// <summary>
    /// 建立測試用的 PlaceInfo
    /// </summary>
    public static PlaceInfo CreatePlaceInfo()
    {
        return new PlaceInfo
        {
            Name = "一蘭拉麵 新宿店",
            Address = "東京都新宿區新宿3-34-11",
            Phone = "+81-3-1234-5678",
            Category = "拉麵店",
            Description = "知名的豚骨拉麵連鎖店",
            Confidence = 0.85,
            OriginalTexts = new List<string> { "一蘭拉麵", "新宿店" }
        };
    }

    /// <summary>
    /// 建立測試用的 DetectedText
    /// </summary>
    public static DetectedText CreateDetectedText()
    {
        return new DetectedText
        {
            Text = "一蘭拉麵",
            Confidence = 0.9,
            Language = "ja",
            Location = "招牌"
        };
    }

    /// <summary>
    /// 建立測試用的 SubtitleSegment
    /// </summary>
    public static SubtitleSegment CreateSubtitleSegment()
    {
        return new SubtitleSegment
        {
            StartTime = TimeSpan.FromSeconds(10),
            EndTime = TimeSpan.FromSeconds(15),
            Text = "今天我們來到了東京最有名的拉麵店",
            Language = "zh-TW",
            Confidence = 0.95
        };
    }

    /// <summary>
    /// 建立測試用的 YouTubeVideoInfo
    /// </summary>
    public static YouTubeVideoInfo CreateYouTubeVideoInfo()
    {
        return new YouTubeVideoInfo
        {
            Id = "test123",
            Title = "東京美食探索 - 新宿拉麵店巡禮",
            Url = "https://www.youtube.com/watch?v=test123",
            Description = "探索東京新宿最受歡迎的拉麵店",
            Channel = "美食探險家",
            CategoryName = "旅遊與活動",
            DefaultLanguage = "ja",
            ViewCount = 150000,
            LikeCount = 8500,
            CommentCount = 320,
            UploadDate = DateTime.Parse("2024-01-15"),
            Tags = new List<string> { "拉麵", "東京", "美食", "日本料理", "新宿" },
            ExtractedKeywords = new List<string> { "豚骨拉麵", "味噌拉麵", "東京美食", "新宿" },
            DetectedPlaces = new List<PlaceReference>
            {
                new PlaceReference
                {
                    Name = "一蘭拉麵 新宿店",
                    FormattedAddress = "東京都新宿區新宿3-34-11",
                    Types = new List<string> { "restaurant", "food" },
                    DetectionSource = "title",
                    ConfidenceScore = 0.85
                }
            },
            ChannelInfo = new YouTubeChannelInfo
            {
                Id = "channel123",
                Title = "美食探險家",
                SubscriberCount = 250000,
                Country = "JP"
            }
        };
    }

    /// <summary>
    /// 建立測試用的 VideoAnalysisOptions
    /// </summary>
    public static VideoAnalysisOptions CreateVideoAnalysisOptions()
    {
        return new VideoAnalysisOptions
        {
            FrameExtractionIntervalSeconds = 30,
            UseSceneChangeDetection = true,
            GenerateSubtitlesIfMissing = true,
            EnableTextRecognition = true,
            EnablePlaceRecognition = true,
            OutputFormat = "json",
            MaxFramesPerVideo = 10
        };
    }

    /// <summary>
    /// 建立測試用的 LMStudioOptions
    /// </summary>
    public static LMStudioOptions CreateLMStudioOptions()
    {
        return new LMStudioOptions
        {
            BaseUrl = "http://localhost:1234",
            TextModel = "test-model",
            VisionModel = "test-vision-model",
            MaxTokens = 2000,
            Temperature = 0.7,
            TimeoutSeconds = 30
        };
    }

    /// <summary>
    /// 建立測試用的 JSON 回應字串
    /// </summary>
    public static string CreateValidSceneDescriptionJson()
    {
        return """
        {
            "mainDescription": "一家日式拉麵店的外觀",
            "cuisineType": "日式拉麵",
            "restaurantCategory": "拉麵店",
            "visibleTexts": ["一蘭拉麵", "新宿店"],
            "activities": ["用餐", "排隊"],
            "setting": "東京新宿區",
            "colors": ["紅色", "白色"],
            "mood": "熱鬧"
        }
        """;
    }

    /// <summary>
    /// 建立無效的 JSON 回應字串
    /// </summary>
    public static string CreateInvalidJson()
    {
        return """
        {
            "mainDescription": "一家日式拉麵店的外觀",
            "cuisineType": "日式拉麵",
            "restaurantCategory": "拉麵店"
            // 缺少結尾括號和逗號錯誤
        """;
    }
}
