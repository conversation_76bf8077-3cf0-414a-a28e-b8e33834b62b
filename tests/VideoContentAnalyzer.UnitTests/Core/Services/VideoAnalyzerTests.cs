// using FluentAssertions;
// using Microsoft.Extensions.Logging;
// using System.IO.Abstractions.TestingHelpers;
// using VideoContentAnalyzer.Core.Interfaces;
// using VideoContentAnalyzer.Core.Models;
// using VideoContentAnalyzer.Core.Services;
// using VideoContentAnalyzer.UnitTests.TestHelpers;
// using Xunit;
//
// namespace VideoContentAnalyzer.UnitTests.Core.Services;
//
// /// <summary>
// /// VideoAnalyzer 的單元測試
// /// </summary>
// public class VideoAnalyzerTests
// {
//     private readonly IVideoFrameExtractor _mockFrameExtractor;
//     private readonly IAIAnalysisService _mockAIAnalysisService;
//     private readonly ISubtitleService _mockSubtitleService;
//     private readonly IPlaceDetectionService _mockPlaceDetectionService;
//     private readonly ILogger<VideoAnalyzer> _mockLogger;
//     private readonly MockFileSystem _fileSystem;
//     private readonly VideoAnalyzer _analyzer;
//
//     public VideoAnalyzerTests()
//     {
//         _mockFrameExtractor = Substitute.For<IVideoFrameExtractor>();
//         _mockAIAnalysisService = Substitute.For<IAIAnalysisService>();
//         _mockSubtitleService = Substitute.For<ISubtitleService>();
//         _mockPlaceDetectionService = Substitute.For<IPlaceDetectionService>();
//         _mockLogger = TestFixtures.CreateMockLogger<VideoAnalyzer>();
//         _fileSystem = TestFixtures.CreateMockFileSystem();
//
//         _analyzer = new VideoAnalyzer(
//             _mockFrameExtractor,
//             _mockAIAnalysisService,
//             _mockSubtitleService,
//             _mockPlaceDetectionService,
//             _mockLogger,
//             _fileSystem
//         );
//     }
//
//     #region AnalyzeVideoAsync Tests (Basic Version)
//
//     [Fact()]
//     public async Task AnalyzeVideoAsync_WithValidVideoPath_ShouldReturnCompleteAnalysis()
//     {
//         // Arrange
//         var videoPath = "/test/video.mp4";
//         var request = new VideoAnalysisRequest
//         {
//             VideoPath = videoPath,
//             Options = MockData.CreateVideoAnalysisOptions()
//         };
//
//         SetupSuccessfulAnalysisMocks(videoPath);
//
//         // Act
//         var result = await _analyzer.AnalyzeVideoAsync(request, null, CancellationToken.None);
//
//         // Assert
//         result.ShouldBeValidVideoAnalysisResult();
//         result.VideoPath.Should().Be(videoPath);
//         result.VideoDuration.Should().BePositive();
//         result.FrameAnalyses.Should().NotBeEmpty();
//         result.SubtitleSegments.Should().NotBeEmpty();
//
//         VerifyAllServicesCalled();
//     }
//
//     [Fact()]
//     public async Task AnalyzeVideoAsync_WithNonExistentFile_ShouldThrowFileNotFoundException()
//     {
//         // Arrange
//         var nonExistentPath = "/nonexistent/video.mp4";
//         var request = new VideoAnalysisRequest
//         {
//             VideoPath = nonExistentPath,
//             Options = MockData.CreateVideoAnalysisOptions()
//         };
//
//         // Act & Assert
//         await Assert.ThrowsAsync<FileNotFoundException>(
//             () => _analyzer.AnalyzeVideoAsync(request, null, CancellationToken.None));
//     }
//
//     [Fact()]
//     public async Task AnalyzeVideoAsync_WithFrameExtractionFailure_ShouldHandleGracefully()
//     {
//         // Arrange
//         var videoPath = "/test/video.mp4";
//         var request = new VideoAnalysisRequest
//         {
//             VideoPath = videoPath,
//             Options = MockData.CreateVideoAnalysisOptions()
//         };
//
//         _mockFrameExtractor
//             .GetVideoDurationAsync(videoPath, Arg.Any<CancellationToken>())
//             .Returns(TimeSpan.FromMinutes(5));
//
//         _mockFrameExtractor
//             .When(x => x.ExtractFramesAsync(videoPath, Arg.Any<VideoAnalysisOptions>(), Arg.Any<CancellationToken>()))
//             .Do(x => throw new InvalidOperationException("FFmpeg error"));
//
//         // Act & Assert
//         await Assert.ThrowsAsync<InvalidOperationException>(
//             () => _analyzer.AnalyzeVideoAsync(request, null, CancellationToken.None));
//     }
//
//     [Fact()]
//     public async Task AnalyzeVideoAsync_WithCancellationToken_ShouldRespectCancellation()
//     {
//         // Arrange
//         var videoPath = "/test/video.mp4";
//         var request = new VideoAnalysisRequest
//         {
//             VideoPath = videoPath,
//             Options = MockData.CreateVideoAnalysisOptions()
//         };
//         var cancellationToken = TestFixtures.CreateCancelledToken();
//
//         // Act & Assert
//         await Assert.ThrowsAsync<OperationCanceledException>(
//             () => _analyzer.AnalyzeVideoAsync(request, cancellationToken));
//     }
//
//     #endregion
//
//     #region AnalyzeVideoAsync Tests (With Progress Reporting)
//
//     [Fact()]
//     public async Task AnalyzeVideoAsync_WithProgressReporting_ShouldReportProgress()
//     {
//         // Arrange
//         var videoPath = "/test/video.mp4";
//         var request = new VideoAnalysisRequest
//         {
//             VideoPath = videoPath,
//             Options = MockData.CreateVideoAnalysisOptions()
//         };
//
//         var progressReports = new List<AnalysisProgress>();
//         var progress = new Progress<AnalysisProgress>(report => progressReports.Add(report));
//
//         SetupSuccessfulAnalysisMocks(videoPath);
//
//         // Act
//         var result = await _analyzer.AnalyzeVideoAsync(request, progress);
//
//         // Assert
//         result.ShouldBeValidVideoAnalysisResult();
//         progressReports.Should().NotBeEmpty();
//         progressReports.Should().Contain(p => p.CurrentStage == "Analyzing video metadata");
//         progressReports.Should().Contain(p => p.CurrentStage == "Extracting video frames");
//         progressReports.Should().Contain(p => p.CurrentStage == "AI Analysis");
//         progressReports.Should().Contain(p => p.CurrentStage == "Subtitle Analysis");
//         progressReports.Should().Contain(p => p.CurrentStage == "Generating Summary");
//
//         // 驗證進度百分比遞增
//         var percentages = progressReports.Select(p => p.ProgressPercentage).ToList();
//         percentages.Should().BeInAscendingOrder();
//         percentages.Last().Should().Be(100);
//     }
//
//     [Fact()]
//     public async Task AnalyzeVideoAsync_WithProgressReporting_ShouldIncludeElapsedTime()
//     {
//         // Arrange
//         var videoPath = "/test/video.mp4";
//         var request = new VideoAnalysisRequest
//         {
//             VideoPath = videoPath,
//             Options = MockData.CreateVideoAnalysisOptions()
//         };
//
//         var progressReports = new List<AnalysisProgress>();
//         var progress = new Progress<AnalysisProgress>(report => progressReports.Add(report));
//
//         SetupSuccessfulAnalysisMocks(videoPath);
//
//         // Act
//         var result = await _analyzer.AnalyzeVideoAsync(request, progress);
//
//         // Assert
//         progressReports.Should().AllSatisfy(report =>
//         {
//             report.Elapsed.Should().BeGreaterOrEqualTo(TimeSpan.Zero);
//             report.Message.Should().NotBeNullOrEmpty();
//         });
//
//         // 驗證時間遞增
//         var elapsedTimes = progressReports.Select(p => p.Elapsed).ToList();
//         for (int i = 1; i < elapsedTimes.Count; i++)
//         {
//             elapsedTimes[i].Should().BeGreaterOrEqualTo(elapsedTimes[i - 1]);
//         }
//     }
//
//     [Fact()]
//     public async Task AnalyzeVideoAsync_WithYouTubeMetadata_ShouldPassMetadataToAIService()
//     {
//         // Arrange
//         var videoPath = "/test/video.mp4";
//         var youtubeMetadata = MockData.CreateYouTubeVideoInfo();
//         var request = new VideoAnalysisRequest
//         {
//             VideoPath = videoPath,
//             Options = MockData.CreateVideoAnalysisOptions(),
//             YouTubeMetadata = youtubeMetadata
//         };
//
//         SetupSuccessfulAnalysisMocks(videoPath);
//
//         // Act
//         var result = await _analyzer.AnalyzeVideoAsync(request, null, CancellationToken.None);
//
//         // Assert
//         result.ShouldBeValidVideoAnalysisResult();
//
//         // 驗證 AI 服務收到了 YouTube 元數據
//         _mockAIAnalysisService.Received()
//             .AnalyzeFrameWithTimingAsync(
//                 Arg.Any<string>(),
//                 youtubeMetadata,
//                 Arg.Any<CancellationToken>());
//
//         _mockAIAnalysisService.Received(1)
//             .GenerateVideoSummaryAsync(
//                 Arg.Any<List<FrameAnalysis>>(),
//                 Arg.Any<List<SubtitleSegment>>(),
//                 youtubeMetadata,
//                 Arg.Any<CancellationToken>());
//     }
//
//     [Fact()]
//     public async Task AnalyzeVideoAsync_WithPartialAIFailures_ShouldContinueProcessing()
//     {
//         // Arrange
//         var videoPath = "/test/video.mp4";
//         var request = new VideoAnalysisRequest
//         {
//             VideoPath = videoPath,
//             Options = MockData.CreateVideoAnalysisOptions()
//         };
//
//         SetupPartialFailureAnalysisMocks(videoPath);
//
//         // Act
//         var result = await _analyzer.AnalyzeVideoAsync(request, null, CancellationToken.None);
//
//         // Assert
//         result.ShouldBeValidVideoAnalysisResult();
//         result.FrameAnalyses.Should().NotBeEmpty();
//
//         // 應該有一些成功的分析和一些失敗的分析
//         result.FrameAnalyses.Should().Contain(f => f.Scene.MainDescription != "Analysis failed");
//         result.FrameAnalyses.Should().Contain(f => f.Scene.MainDescription == "Analysis failed");
//     }
//
//     [Fact()]
//     public async Task AnalyzeVideoAsync_WithDisabledTextRecognition_ShouldSkipTextExtraction()
//     {
//         // Arrange
//         var videoPath = "/test/video.mp4";
//         var options = MockData.CreateVideoAnalysisOptions();
//         options.EnableTextRecognition = false;
//
//         var request = new VideoAnalysisRequest
//         {
//             VideoPath = videoPath,
//             Options = options
//         };
//
//         SetupSuccessfulAnalysisMocks(videoPath);
//
//         // Act
//         var result = await _analyzer.AnalyzeVideoAsync(request, null, CancellationToken.None);
//
//         // Assert
//         result.ShouldBeValidVideoAnalysisResult();
//
//         // 驗證沒有呼叫文字提取
//         _mockAIAnalysisService.DidNotReceive()
//             .ExtractTextFromFrameAsync(Arg.Any<string>(), Arg.Any<CancellationToken>());
//     }
//
//     #endregion
//
//     #region Helper Methods
//
//     private void SetupSuccessfulAnalysisMocks(string videoPath)
//     {
//         // 設定影片時長
//         _mockFrameExtractor
//             .GetVideoDurationAsync(videoPath, Arg.Any<CancellationToken>())
//             .Returns(TimeSpan.FromMinutes(5));
//
//         // 設定幀擷取
//         var extractedFrames = new List<ExtractedFrame>
//         {
//             new ExtractedFrame { Timestamp = TimeSpan.FromSeconds(30), FramePath = "/test/frame1.jpg" },
//             new ExtractedFrame { Timestamp = TimeSpan.FromSeconds(60), FramePath = "/test/frame2.jpg" }
//         };
//
//         _mockFrameExtractor
//             .ExtractFramesAsync(videoPath, Arg.Any<VideoAnalysisOptions>(), Arg.Any<CancellationToken>())
//             .Returns(extractedFrames);
//
//         // 設定 AI 分析
//         var sceneDescription = MockData.CreateSceneDescription();
//         _mockAIAnalysisService
//             .AnalyzeFrameWithTimingAsync(Arg.Any<string>(), Arg.Any<CancellationToken>())
//             .Returns((sceneDescription, TimeSpan.FromMilliseconds(500)));
//
//         _mockAIAnalysisService
//             .AnalyzeFrameWithTimingAsync(Arg.Any<string>(), Arg.Any<YouTubeVideoInfo>(), Arg.Any<CancellationToken>())
//             .Returns((sceneDescription, TimeSpan.FromMilliseconds(500)));
//
//         var detectedTexts = new List<DetectedText> { MockData.CreateDetectedText() };
//         _mockAIAnalysisService
//             .ExtractTextFromFrameAsync(Arg.Any<string>(), Arg.Any<CancellationToken>())
//             .Returns(detectedTexts);
//
//         // 設定字幕生成
//         var subtitleSegments = new List<SubtitleSegment> { MockData.CreateSubtitleSegment() };
//         _mockSubtitleService
//             .GenerateSubtitlesAsync(videoPath, Arg.Any<CancellationToken>())
//             .Returns(subtitleSegments);
//
//         // 設定摘要生成
//         _mockAIAnalysisService
//             .GenerateVideoSummaryAsync(
//                 Arg.Any<List<FrameAnalysis>>(),
//                 Arg.Any<List<SubtitleSegment>>(),
//                 Arg.Any<CancellationToken>())
//             .Returns(("影片摘要", "字幕內容", new List<string> { "字幕段落" }));
//
//         _mockAIAnalysisService
//             .GenerateVideoSummaryAsync(
//                 Arg.Any<List<FrameAnalysis>>(),
//                 Arg.Any<List<SubtitleSegment>>(),
//                 Arg.Any<YouTubeVideoInfo>(),
//                 Arg.Any<CancellationToken>())
//             .Returns(("影片摘要", "字幕內容", new List<string> { "字幕段落" }));
//     }
//
//     private void SetupPartialFailureAnalysisMocks(string videoPath)
//     {
//         SetupSuccessfulAnalysisMocks(videoPath);
//
//         // 設定部分 AI 分析失敗
//         var sceneDescription = MockData.CreateSceneDescription();
//         var failedDescription = new SceneDescription { MainDescription = "Analysis failed" };
//
//         var callCount = 0;
//         _mockAIAnalysisService
//             .AnalyzeFrameWithTimingAsync(Arg.Any<string>(), Arg.Any<CancellationToken>())
//             .Returns(x =>
//             {
//                 callCount++;
//                 if (callCount == 1)
//                     return Task.FromResult((sceneDescription, TimeSpan.FromMilliseconds(500)));
//                 else
//                     return Task.FromResult((failedDescription, TimeSpan.Zero));
//             });
//     }
//
//     private void VerifyAllServicesCalled()
//     {
//         _mockFrameExtractor.Received(1)
//             .GetVideoDurationAsync(Arg.Any<string>(), Arg.Any<CancellationToken>());
//
//         _mockFrameExtractor.Received(1)
//             .ExtractFramesAsync(Arg.Any<string>(), Arg.Any<VideoAnalysisOptions>(), Arg.Any<CancellationToken>());
//
//         _mockAIAnalysisService.Received()
//             .AnalyzeFrameWithTimingAsync(Arg.Any<string>(), Arg.Any<CancellationToken>());
//
//         _mockSubtitleService.Received(1)
//             .GenerateSubtitlesAsync(Arg.Any<string>(), Arg.Any<CancellationToken>());
//
//         _mockAIAnalysisService.Received(1)
//             .GenerateVideoSummaryAsync(
//                 Arg.Any<List<FrameAnalysis>>(),
//                 Arg.Any<List<SubtitleSegment>>(),
//                 Arg.Any<CancellationToken>());
//     }
//
//     #endregion
// }
