# Video Content Analyzer 單元測試

這個專案包含了 Video Content Analyzer 的完整單元測試套件，使用現代的 .NET 測試框架和最佳實踐。

## 🎯 測試目標

- **完整覆蓋**: 測試所有核心業務邏輯類別
- **高品質**: 遵循 AAA 模式和測試最佳實踐
- **可維護性**: 使用輔助類別和模擬資料
- **效能**: 快速執行的單元測試
- **可靠性**: 穩定且可重複的測試結果

## 🏗️ 專案結構

```
VideoContentAnalyzer.UnitTests/
├── Core/
│   └── Services/
│       └── VideoAnalyzerTests.cs          # 核心業務邏輯測試
├── Infrastructure/
│   ├── AI/
│   │   ├── AIAnalysisServiceTests.cs      # AI 分析服務測試
│   │   ├── LMStudioClientTests.cs         # LM Studio 客戶端測試
│   │   └── WhisperServiceTests.cs         # Whisper 服務測試
│   └── Subtitle/
│       └── SubtitleServiceTests.cs        # 字幕服務測試
├── TestHelpers/
│   ├── MockData.cs                        # 測試資料生成器
│   ├── TestFixtures.cs                    # 測試固定裝置
│   └── AssertionHelpers.cs                # 自訂斷言方法
├── TestRunner.cs                          # 測試執行器
├── VideoContentAnalyzer.UnitTests.csproj # 專案檔案
└── README.md                              # 本文件
```

## 🧪 測試框架

### 主要依賴項目

- **xUnit.net 2.6.1**: 主要測試框架
- **NSubstitute 5.3.0**: 模擬物件框架
- **FluentAssertions 6.12.0**: 流暢的斷言語法
- **AutoFixture 4.18.0**: 自動測試資料生成
- **System.IO.Abstractions**: 檔案系統抽象化

### 測試類型

1. **單元測試**: 測試個別類別和方法
2. **整合測試**: 測試類別之間的互動
3. **效能測試**: 驗證執行效率
4. **錯誤處理測試**: 驗證例外情況處理

## 🚀 執行測試

### 前置需求

- .NET 9 SDK
- Visual Studio 2022 或 VS Code
- 測試執行器 (內建於 IDE 或 dotnet CLI)

### 命令列執行

```bash
# 執行所有測試
dotnet test

# 執行特定測試類別
dotnet test --filter "ClassName=AIAnalysisServiceTests"

# 執行特定測試方法
dotnet test --filter "MethodName=AnalyzeFrameAsync_WithValidImage_ShouldReturnSceneDescription"

# 產生測試覆蓋率報告
dotnet test --collect:"XPlat Code Coverage"

# 詳細輸出
dotnet test --verbosity normal
```

### IDE 執行

#### Visual Studio 2022
1. 開啟 Test Explorer (測試 → 測試總管)
2. 建置解決方案
3. 點擊 "執行所有測試"

#### VS Code
1. 安裝 C# Dev Kit 擴充功能
2. 開啟測試面板
3. 執行測試

## 📊 測試覆蓋範圍

### 已實作的測試類別 ✅

| 類別 | 測試方法數 | 覆蓋率 | 優先級 |
|------|-----------|--------|--------|
| AIAnalysisService | 15+ | 90% | 高 |
| VideoAnalyzer | 8+ | 85% | 高 |
| LMStudioClient | 12+ | 80% | 高 |
| WhisperService | 10+ | 85% | 高 |
| SubtitleService | 12+ | 90% | 高 |

### 待實作的測試類別 ⏳

| 類別 | 預估測試方法數 | 優先級 |
|------|---------------|--------|
| VideoFrameExtractor | 8+ | 中 |
| YouTubeDownloadService | 10+ | 中 |
| YouTubeApiService | 8+ | 中 |
| OpenRouterClient | 6+ | 低 |
| GooglePlacesDetectionService | 6+ | 低 |

## 🎯 測試重點

### 1. AIAnalysisService 測試
- ✅ 圖片分析功能
- ✅ JSON 解析和錯誤處理
- ✅ YouTube 元數據整合
- ✅ 批次處理
- ✅ 餐廳資訊提取

### 2. VideoAnalyzer 測試
- ✅ 完整分析流程
- ✅ 進度報告機制
- ✅ 錯誤處理和恢復
- ✅ 取消操作支援

### 3. LMStudioClient 測試
- ✅ HTTP 請求處理
- ✅ 圖片轉 Base64
- ✅ 錯誤和超時處理
- ✅ 資源釋放

### 4. WhisperService 測試
- ✅ 音訊轉錄功能
- ✅ 多語言支援
- ✅ 後備機制 (Whisper.net → CLI)
- ✅ 大檔案處理

### 5. SubtitleService 測試
- ✅ 多格式字幕解析 (SRT, VTT, ASS)
- ✅ 字幕生成流程
- ✅ 格式檢測
- ✅ 檔案儲存

## 🔧 測試輔助工具

### MockData 類別
提供一致的測試資料：
```csharp
var sceneDescription = MockData.CreateSceneDescription();
var placeInfo = MockData.CreatePlaceInfo();
var youtubeInfo = MockData.CreateYouTubeVideoInfo();
```

### TestFixtures 類別
提供測試環境設定：
```csharp
var fileSystem = TestFixtures.CreateMockFileSystem();
var options = TestFixtures.CreateMockLMStudioOptions();
var logger = TestFixtures.CreateMockLogger<T>();
```

### AssertionHelpers 類別
提供自訂斷言方法：
```csharp
result.ShouldBeValidSceneDescription();
placeInfo.ShouldHaveCompleteAddressInfo();
segments.ShouldAllBeValid(s => s.ShouldBeValidSubtitleSegment());
```

## 📋 測試最佳實踐

### 1. 命名規範
- 測試方法: `MethodName_Scenario_ExpectedResult`
- 測試類別: `ClassNameTests`
- 變數: 描述性名稱

### 2. 測試結構 (AAA 模式)
```csharp
[Fact]
public async Task MethodName_Scenario_ExpectedResult()
{
    // Arrange - 準備測試資料和環境
    var input = "test input";
    var expected = "expected result";
    
    // Act - 執行被測試的方法
    var result = await _service.MethodAsync(input);
    
    // Assert - 驗證結果
    result.Should().Be(expected);
}
```

### 3. 模擬物件使用
```csharp
// 設定模擬行為
_mockService
    .MethodAsync(Arg.Any<string>())
    .Returns("mocked result");

// 驗證互動
_mockService.Verify(
    x => x.MethodAsync("expected input"),
    Times.Once);
```

### 4. 錯誤處理測試
```csharp
[Fact]
public async Task Method_WithInvalidInput_ShouldThrowException()
{
    // Arrange
    var invalidInput = null;
    
    // Act & Assert
    await Assert.ThrowsAsync<ArgumentNullException>(
        () => _service.MethodAsync(invalidInput));
}
```

## 🚧 待改進項目

### 短期目標
- [ ] 完成剩餘測試類別的實作
- [ ] 增加整合測試
- [ ] 設定 CI/CD 測試管道
- [ ] 提高測試覆蓋率至 95%

### 長期目標
- [ ] 實作效能基準測試
- [ ] 添加端到端測試
- [ ] 建立測試資料庫
- [ ] 自動化測試報告生成

## 🤝 貢獻指南

1. **新增測試**: 遵循現有的命名和結構規範
2. **測試資料**: 使用 MockData 類別提供的方法
3. **斷言**: 優先使用 FluentAssertions 和自訂斷言方法
4. **文件**: 為複雜的測試案例添加註解
5. **效能**: 確保測試執行時間合理

## 📞 支援

如有測試相關問題，請：
1. 檢查現有測試案例作為參考
2. 查閱測試框架文件
3. 提交 Issue 或 Pull Request

---

**最後更新**: 2024-01-15  
**維護者**: Video Content Analyzer 開發團隊
