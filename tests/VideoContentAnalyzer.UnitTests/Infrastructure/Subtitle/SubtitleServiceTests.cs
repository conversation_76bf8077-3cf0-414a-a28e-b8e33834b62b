// using FluentAssertions;
// using Microsoft.Extensions.Logging;
// using NSubstitute;
// using System.IO.Abstractions;
// using VideoContentAnalyzer.Core.Interfaces;
// using VideoContentAnalyzer.Core.Models;
// using VideoContentAnalyzer.Infrastructure.Subtitle;
// using VideoContentAnalyzer.UnitTests.TestHelpers;
// using Xunit;
//
// namespace VideoContentAnalyzer.UnitTests.Infrastructure.Subtitle;
//
// /// <summary>
// /// SubtitleService 的單元測試
// /// </summary>
// public class SubtitleServiceTests
// {
//     private readonly IWhisperService _mockWhisperService;
//     private readonly ILogger<SubtitleService> _mockLogger;
//     private readonly IFileSystem _fileSystem;
//     private readonly SubtitleService _service;
//
//     public SubtitleServiceTests()
//     {
//         _mockWhisperService = Substitute.For<IWhisperService>();
//         _mockLogger = TestFixtures.CreateMockLogger<SubtitleService>();
//         _fileSystem = TestFixtures.CreateMockFileSystem();
//
//         // 現在 SubtitleService 支援 IFileSystem 依賴注入
//         _service = new SubtitleService(_mockWhisperService, _mockLogger, _fileSystem);
//     }
//
//     #region ParseSubtitleFileAsync Tests
//
//     [Fact(Skip = "需要實際的字幕解析器環境")]
//     public async Task ParseSubtitleFileAsync_WithSrtFile_ShouldReturnCorrectSegments()
//     {
//         // Arrange
//         var srtPath = "/test/subtitle.srt";
//         var srtContent = """
//         1
//         00:00:10,000 --> 00:00:15,000
//         今天我們來到了東京最有名的拉麵店
//
//         2
//         00:00:15,500 --> 00:00:20,000
//         這家店的豚骨拉麵非常有名
//
//         3
//         00:00:20,500 --> 00:00:25,000
//         讓我們一起來品嚐看看
//         """;
//
//         _fileSystem.File.WriteAllText(srtPath, srtContent);
//
//         // Act
//         var result = await _service.ParseSubtitleFileAsync(srtPath);
//
//         // Assert
//         result.Should().HaveCount(3);
//
//         result[0].StartTime.Should().Be(TimeSpan.FromSeconds(10));
//         result[0].EndTime.Should().Be(TimeSpan.FromSeconds(15));
//         result[0].Text.Should().Be("今天我們來到了東京最有名的拉麵店");
//
//         result[1].StartTime.Should().Be(TimeSpan.FromMilliseconds(15500));
//         result[1].EndTime.Should().Be(TimeSpan.FromSeconds(20));
//         result[1].Text.Should().Be("這家店的豚骨拉麵非常有名");
//
//         result[2].StartTime.Should().Be(TimeSpan.FromMilliseconds(20500));
//         result[2].EndTime.Should().Be(TimeSpan.FromSeconds(25));
//         result[2].Text.Should().Be("讓我們一起來品嚐看看");
//
//         result.Should().AllSatisfy(segment => segment.ShouldBeValidSubtitleSegment());
//     }
//
//     [Fact(Skip = "需要實際的字幕解析器環境")]
//     public async Task ParseSubtitleFileAsync_WithVttFile_ShouldReturnCorrectSegments()
//     {
//         // Arrange
//         var vttPath = "/test/subtitle.vtt";
//         var vttContent = """
//         WEBVTT
//
//         00:00:10.000 --> 00:00:15.000
//         今天我們來到了東京最有名的拉麵店
//
//         00:00:15.500 --> 00:00:20.000
//         這家店的豚骨拉麵非常有名
//
//         00:00:20.500 --> 00:00:25.000
//         讓我們一起來品嚐看看
//         """;
//
//         _fileSystem.File.WriteAllText(vttPath, vttContent);
//
//         // Act
//         var result = await _service.ParseSubtitleFileAsync(vttPath);
//
//         // Assert
//         result.Should().HaveCount(3);
//         result.Should().AllSatisfy(segment => segment.ShouldBeValidSubtitleSegment());
//
//         // 驗證 VTT 特定的解析
//         result[0].Text.Should().Be("今天我們來到了東京最有名的拉麵店");
//     }
//
//     [Fact(Skip = "需要實際的字幕解析器環境")]
//     public async Task ParseSubtitleFileAsync_WithAssFile_ShouldReturnCorrectSegments()
//     {
//         // Arrange
//         var assPath = "/test/subtitle.ass";
//         var assContent = """
//         [Script Info]
//         Title: Test Subtitle
//         ScriptType: v4.00+
//
//         [V4+ Styles]
//         Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
//         Style: Default,Arial,20,&H00FFFFFF,&H000000FF,&H00000000,&H80000000,0,0,0,0,100,100,0,0,1,2,0,2,10,10,10,1
//
//         [Events]
//         Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
//         Dialogue: 0,0:00:10.00,0:00:15.00,Default,,0,0,0,,今天我們來到了東京最有名的拉麵店
//         Dialogue: 0,0:00:15.50,0:00:20.00,Default,,0,0,0,,這家店的豚骨拉麵非常有名
//         """;
//
//         _fileSystem.File.WriteAllText(assPath, assContent);
//
//         // Act
//         var result = await _service.ParseSubtitleFileAsync(assPath);
//
//         // Assert
//         result.Should().HaveCount(2);
//         result.Should().AllSatisfy(segment => segment.ShouldBeValidSubtitleSegment());
//     }
//
//     [Fact(Skip = "需要實際的字幕解析器環境")]
//     public async Task ParseSubtitleFileAsync_WithUnknownFormat_ShouldUseGenericParser()
//     {
//         // Arrange
//         var unknownPath = "/test/subtitle.unknown";
//         var content = "Some subtitle content";
//         _fileSystem.File.WriteAllText(unknownPath, content);
//
//         // Act
//         var result = await _service.ParseSubtitleFileAsync(unknownPath);
//
//         // Assert
//         result.Should().NotBeNull();
//         TestFixtures.VerifyLoggerCalledWithMessage(_mockLogger, LogLevel.Warning, "Unknown subtitle format");
//     }
//
//     [Fact(Skip = "需要實際的字幕解析器環境")]
//     public async Task ParseSubtitleFileAsync_WithNonExistentFile_ShouldThrowFileNotFoundException()
//     {
//         // Arrange
//         var nonExistentPath = "/nonexistent/subtitle.srt";
//
//         // Act & Assert
//         await Assert.ThrowsAsync<FileNotFoundException>(
//             () => _service.ParseSubtitleFileAsync(nonExistentPath));
//     }
//
//     [Fact(Skip = "需要實際的字幕解析器環境")]
//     public async Task ParseSubtitleFileAsync_WithCorruptedFile_ShouldThrowException()
//     {
//         // Arrange
//         var corruptedPath = "/test/corrupted.srt";
//         var corruptedContent = "This is not a valid subtitle file";
//         _fileSystem.File.WriteAllText(corruptedPath, corruptedContent);
//
//         // Act & Assert
//         // 實際測試需要模擬解析器拋出例外
//         await Assert.ThrowsAsync<Exception>(
//             () => _service.ParseSubtitleFileAsync(corruptedPath));
//     }
//
//     [Fact(Skip = "需要實際的字幕解析器環境")]
//     public async Task ParseSubtitleFileAsync_WithEmptyFile_ShouldReturnEmptyList()
//     {
//         // Arrange
//         var emptyPath = "/test/empty.srt";
//         _fileSystem.File.WriteAllText(emptyPath, "");
//
//         // Act
//         var result = await _service.ParseSubtitleFileAsync(emptyPath);
//
//         // Assert
//         result.Should().BeEmpty();
//     }
//
//     [Fact(Skip = "需要實際的字幕解析器環境")]
//     public async Task ParseSubtitleFileAsync_WithCancellationToken_ShouldRespectCancellation()
//     {
//         // Arrange
//         var subtitlePath = "/test/subtitle.srt";
//         _fileSystem.File.WriteAllText(subtitlePath, "fake content");
//         var cancellationToken = TestFixtures.CreateCancelledToken();
//
//         // Act & Assert
//         await Assert.ThrowsAsync<OperationCanceledException>(
//             () => _service.ParseSubtitleFileAsync(subtitlePath, cancellationToken));
//     }
//
//     #endregion
//
//     #region GenerateSubtitlesAsync Tests
//
//     [Fact(Skip = "需要實際的字幕解析器環境")]
//     public async Task GenerateSubtitlesAsync_WithValidVideo_ShouldReturnSubtitleSegments()
//     {
//         // Arrange
//         var videoPath = "/test/video.mp4";
//         var audioPath = "/test/audio.wav";
//         var expectedSegments = new List<SubtitleSegment>
//         {
//             MockData.CreateSubtitleSegment()
//         };
//
//         _mockWhisperService
//             .ExtractAudioFromVideoAsync(videoPath, Arg.Any<CancellationToken>())
//             .Returns(Task.FromResult(audioPath));
//
//         _mockWhisperService
//             .TranscribeAudioAsync(audioPath, Arg.Any<CancellationToken>())
//             .Returns(expectedSegments);
//
//         // Act
//         var result = await _service.GenerateSubtitlesAsync(videoPath);
//
//         // Assert
//         result.Should().HaveCount(1);
//         result.Should().AllSatisfy(segment => segment.ShouldBeValidSubtitleSegment());
//
//         _mockWhisperService.Received(1)
//             .ExtractAudioFromVideoAsync(videoPath, Arg.Any<CancellationToken>());
//
//         _mockWhisperService.Received(1)
//             .TranscribeAudioAsync(audioPath, Arg.Any<CancellationToken>());
//
//         TestFixtures.VerifyLoggerCalledWithMessage(_mockLogger, LogLevel.Information, "Generating subtitles for video");
//     }
//
//     [Fact(Skip = "需要實際的字幕解析器環境")]
//     public async Task GenerateSubtitlesAsync_WithNonExistentVideo_ShouldThrowFileNotFoundException()
//     {
//         // Arrange
//         var nonExistentPath = "/nonexistent/video.mp4";
//
//         _mockWhisperService
//             .When(x => x.ExtractAudioFromVideoAsync(nonExistentPath, Arg.Any<CancellationToken>()))
//             .Do(x => throw new FileNotFoundException());
//
//         // Act & Assert
//         await Assert.ThrowsAsync<FileNotFoundException>(
//             () => _service.GenerateSubtitlesAsync(nonExistentPath));
//     }
//
//     [Fact(Skip = "需要實際的字幕解析器環境")]
//     public async Task GenerateSubtitlesAsync_WhenAudioExtractionFails_ShouldThrowException()
//     {
//         // Arrange
//         var videoPath = "/test/video.mp4";
//
//         _mockWhisperService
//             .When(x => x.ExtractAudioFromVideoAsync(videoPath, Arg.Any<CancellationToken>()))
//             .Do(x => throw new InvalidOperationException("FFmpeg error"));
//
//         // Act & Assert
//         await Assert.ThrowsAsync<InvalidOperationException>(
//             () => _service.GenerateSubtitlesAsync(videoPath));
//     }
//
//     [Fact(Skip = "需要實際的字幕解析器環境")]
//     public async Task GenerateSubtitlesAsync_WhenTranscriptionFails_ShouldThrowException()
//     {
//         // Arrange
//         var videoPath = "/test/video.mp4";
//         var audioPath = "/test/audio.wav";
//
//         _mockWhisperService
//             .ExtractAudioFromVideoAsync(videoPath, Arg.Any<CancellationToken>())
//             .Returns(audioPath);
//
//         _mockWhisperService
//             .When(x => x.TranscribeAudioAsync(audioPath, Arg.Any<CancellationToken>()))
//             .Do(x => throw new InvalidOperationException("Whisper error"));
//
//         // Act & Assert
//         await Assert.ThrowsAsync<InvalidOperationException>(
//             () => _service.GenerateSubtitlesAsync(videoPath));
//     }
//
//     [Fact(Skip = "需要實際的字幕解析器環境")]
//     public async Task GenerateSubtitlesAsync_WithCancellationToken_ShouldRespectCancellation()
//     {
//         // Arrange
//         var videoPath = "/test/video.mp4";
//         var cancellationToken = TestFixtures.CreateCancelledToken();
//
//         // Act & Assert
//         await Assert.ThrowsAsync<OperationCanceledException>(
//             () => _service.GenerateSubtitlesAsync(videoPath, cancellationToken));
//     }
//
//     [Fact(Skip = "需要實際的字幕解析器環境")]
//     public async Task GenerateSubtitlesAsync_WithLongVideo_ShouldHandleEfficiently()
//     {
//         // Arrange
//         var videoPath = "/test/long_video.mp4";
//         var audioPath = "/test/long_audio.wav";
//         var manySegments = Enumerable.Range(0, 100)
//             .Select(i => MockData.CreateSubtitleSegment())
//             .ToList();
//
//         _mockWhisperService
//             .ExtractAudioFromVideoAsync(videoPath, Arg.Any<CancellationToken>())
//             .Returns(audioPath);
//
//         _mockWhisperService
//             .TranscribeAudioAsync(audioPath, Arg.Any<CancellationToken>())
//             .Returns(manySegments);
//
//         // Act
//         var stopwatch = System.Diagnostics.Stopwatch.StartNew();
//         var result = await _service.GenerateSubtitlesAsync(videoPath);
//         stopwatch.Stop();
//
//         // Assert
//         result.Should().HaveCount(100);
//         stopwatch.ElapsedMilliseconds.Should().BeLessThan(10000); // 10秒內完成
//     }
//
//     #endregion
//
//     #region DetectSubtitleFormatAsync Tests
//
//     [Theory]
//     [InlineData("/test/subtitle.srt", "srt")]
//     [InlineData("/test/subtitle.vtt", "vtt")]
//     [InlineData("/test/subtitle.ass", "ass")]
//     [InlineData("/test/subtitle.ssa", "ssa")]
//     [InlineData("/test/subtitle.unknown", "unknown")]
//     public async Task DetectSubtitleFormatAsync_WithDifferentExtensions_ShouldReturnCorrectFormat(string filePath, string expectedFormat)
//     {
//         // Arrange
//         _fileSystem.File.WriteAllText(filePath, "fake content");
//
//         // Act
//         var result = await _service.DetectSubtitleFormatAsync(filePath);
//
//         // Assert
//         result.Should().Be(expectedFormat);
//     }
//
//     [Fact(Skip = "需要實際的字幕解析器環境")]
//     public async Task DetectSubtitleFormatAsync_WithContentBasedDetection_ShouldDetectCorrectly()
//     {
//         // Arrange
//         var filePath = "/test/subtitle.txt";
//         var vttContent = "WEBVTT\n\n00:00:10.000 --> 00:00:15.000\nTest subtitle";
//         _fileSystem.File.WriteAllText(filePath, vttContent);
//
//         // Act
//         var result = await _service.DetectSubtitleFormatAsync(filePath);
//
//         // Assert
//         // 如果實作了內容檢測，應該返回 "vtt"
//         // 否則基於副檔名返回 "unknown"
//         result.Should().BeOneOf("vtt", "unknown");
//     }
//
//     #endregion
//
//     #region SaveSubtitlesAsync Tests
//     // TODO: SaveSubtitlesAsync 方法尚未在 SubtitleService 中實現，暫時註釋掉這些測試
//
//     /*
//     [Fact(Skip = "需要實際的字幕解析器環境")]
//     public async Task SaveSubtitlesAsync_WithSrtFormat_ShouldCreateValidSrtFile()
//     {
//         // Arrange
//         var segments = new List<Core.Models.SubtitleSegment>
//         {
//             new Core.Models.SubtitleSegment
//             {
//                 StartTime = TimeSpan.FromSeconds(10),
//                 EndTime = TimeSpan.FromSeconds(15),
//                 Text = "第一段字幕"
//             },
//             new Core.Models.SubtitleSegment
//             {
//                 StartTime = TimeSpan.FromSeconds(15),
//                 EndTime = TimeSpan.FromSeconds(20),
//                 Text = "第二段字幕"
//             }
//         };
//
//         var outputPath = "/test/output.srt";
//
//         // Act
//         await _service.SaveSubtitlesAsync(segments, outputPath, "srt");
//
//         // Assert
//         _fileSystem.File.Exists(outputPath).Should().BeTrue();
//         var content = _fileSystem.File.ReadAllText(outputPath);
//
//         content.Should().Contain("1\n00:00:10,000 --> 00:00:15,000\n第一段字幕");
//         content.Should().Contain("2\n00:00:15,000 --> 00:00:20,000\n第二段字幕");
//     }
//
//     [Fact(Skip = "需要實際的字幕解析器環境")]
//     public async Task SaveSubtitlesAsync_WithVttFormat_ShouldCreateValidVttFile()
//     {
//         // Arrange
//         var segments = new List<Core.Models.SubtitleSegment>
//         {
//             MockData.CreateSubtitleSegment()
//         };
//
//         var outputPath = "/test/output.vtt";
//
//         // Act
//         await _service.SaveSubtitlesAsync(segments, outputPath, "vtt");
//
//         // Assert
//         _fileSystem.File.Exists(outputPath).Should().BeTrue();
//         var content = _fileSystem.File.ReadAllText(outputPath);
//
//         content.Should().StartWith("WEBVTT");
//         content.Should().Contain("00:00:10.000 --> 00:00:15.000");
//     }
//
//     [Fact(Skip = "需要實際的字幕解析器環境")]
//     public async Task SaveSubtitlesAsync_WithEmptySegments_ShouldCreateEmptyFile()
//     {
//         // Arrange
//         var emptySegments = new List<Core.Models.SubtitleSegment>();
//         var outputPath = "/test/empty.srt";
//
//         // Act
//         await _service.SaveSubtitlesAsync(emptySegments, outputPath, "srt");
//
//         // Assert
//         _fileSystem.File.Exists(outputPath).Should().BeTrue();
//         var content = _fileSystem.File.ReadAllText(outputPath);
//         content.Should().BeEmpty();
//     }
//     */
//
//     #endregion
// }
