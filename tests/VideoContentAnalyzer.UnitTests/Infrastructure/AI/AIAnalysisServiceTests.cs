// using FluentAssertions;
// using Microsoft.Extensions.Logging;
// using Microsoft.Extensions.Options;
// using NSubstitute;
// using VideoContentAnalyzer.Core.Interfaces;
// using VideoContentAnalyzer.Core.Models;
// using VideoContentAnalyzer.Infrastructure.AI;
// using VideoContentAnalyzer.UnitTests.TestHelpers;
// using Xunit;
//
// namespace VideoContentAnalyzer.UnitTests.Infrastructure.AI;
//
// /// <summary>
// /// AIAnalysisService 的單元測試
// /// </summary>
// public class AIAnalysisServiceTests
// {
//     private readonly ILMStudioClient _mockLMStudioClient;
//     private readonly IOpenRouterClient _mockOpenRouterClient;
//     private readonly ILogger<AIAnalysisService> _mockLogger;
//     private readonly IYouTubeMetadataFormatter _mockMetadataFormatter;
//     private readonly AIAnalysisService _service;
//
//     public AIAnalysisServiceTests()
//     {
//         _mockLMStudioClient = Substitute.For<ILMStudioClient>();
//         _mockOpenRouterClient = Substitute.For<IOpenRouterClient>();
//         _mockLogger = Substitute.For<ILogger<AIAnalysisService>>();
//         _mockMetadataFormatter = Substitute.For<IYouTubeMetadataFormatter>();
//
//         _service = new AIAnalysisService(
//             _mockLMStudioClient,
//             _mockOpenRouterClient,
//             _mockLogger,
//             _mockMetadataFormatter
//         );
//     }
//
//     #region AnalyzeFrameAsync Tests
//
//     [Fact]
//     public async Task AnalyzeFrameAsync_WithValidImage_ShouldReturnSceneDescription()
//     {
//         // Arrange
//         var framePath = "/test/frame.jpg";
//         var expectedJson = MockData.CreateValidSceneDescriptionJson();
//         var expectedDuration = TimeSpan.FromMilliseconds(500);
//
//         _mockLMStudioClient
//             .SendVisionRequestAsync(framePath, Arg.Any<string>(), Arg.Any<CancellationToken>())
//             .Returns((expectedJson, expectedDuration));
//
//         // Act
//         var result = await _service.AnalyzeFrameAsync(framePath);
//
//         // Assert
//         result.Should().NotBeNull();
//         result.MainDescription.Should().Be("一家日式拉麵店的外觀");
//         result.CuisineType.Should().Be("日式拉麵");
//         result.RestaurantCategory.Should().Be("拉麵店");
//         result.VisibleTexts.Should().Contain("一蘭拉麵");
//
//         _mockLMStudioClient.Received(1)
//             .SendVisionRequestAsync(framePath, Arg.Any<string>(), Arg.Any<CancellationToken>());
//     }
//
//     [Fact]
//     public async Task AnalyzeFrameAsync_WhenLMStudioThrowsException_ShouldReturnFailureDescription()
//     {
//         // Arrange
//         var framePath = "/test/frame.jpg";
//         _mockLMStudioClient
//             .When(x => x.SendVisionRequestAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<CancellationToken>()))
//             .Do(x => throw new HttpRequestException("Connection failed"));
//
//         // Act
//         var result = await _service.AnalyzeFrameAsync(framePath);
//
//         // Assert
//         result.Should().NotBeNull();
//         result.MainDescription.Should().Be("Analysis failed");
//     }
//
//     [Fact]
//     public async Task AnalyzeFrameAsync_WithInvalidJson_ShouldReturnFallbackDescription()
//     {
//         // Arrange
//         var framePath = "/test/frame.jpg";
//         var invalidJson = MockData.CreateInvalidJson();
//         var expectedDuration = TimeSpan.FromMilliseconds(500);
//
//         _mockLMStudioClient
//             .SendVisionRequestAsync(framePath, Arg.Any<string>(), Arg.Any<CancellationToken>())
//             .Returns((invalidJson, expectedDuration));
//
//         // Act
//         var result = await _service.AnalyzeFrameAsync(framePath);
//
//         // Assert
//         result.Should().NotBeNull();
//         result.MainDescription.Should().NotBeEmpty();
//     }
//
//     [Fact]
//     public async Task AnalyzeFrameAsync_WithCancellationToken_ShouldPassTokenToClient()
//     {
//         // Arrange
//         var framePath = "/test/frame.jpg";
//         var cancellationToken = new CancellationToken(true);
//
//         _mockLMStudioClient
//             .When(x => x.SendVisionRequestAsync(Arg.Any<string>(), Arg.Any<string>(), cancellationToken))
//             .Do(x => throw new OperationCanceledException());
//
//         // Act
//         var result = await _service.AnalyzeFrameAsync(framePath, cancellationToken);
//
//         // Assert
//         // AnalyzeFrameAsync 會捕獲所有異常並返回失敗描述，而不是重新拋出異常
//         result.Should().NotBeNull();
//         result.MainDescription.Should().Be("Analysis failed");
//
//         // 驗證 cancellationToken 被正確傳遞給 LMStudioClient
//         _mockLMStudioClient.Received(1)
//             .SendVisionRequestAsync(framePath, Arg.Any<string>(), cancellationToken);
//     }
//
//     #endregion
//
//     #region AnalyzeFrameWithTimingAsync Tests
//
//     [Fact]
//     public async Task AnalyzeFrameWithTimingAsync_WithoutYouTubeMetadata_ShouldReturnResultWithTiming()
//     {
//         // Arrange
//         var framePath = "/test/frame.jpg";
//         var expectedJson = MockData.CreateValidSceneDescriptionJson();
//         var expectedDuration = TimeSpan.FromMilliseconds(750);
//
//         _mockLMStudioClient
//             .SendVisionRequestAsync(framePath, Arg.Any<string>(), Arg.Any<CancellationToken>())
//             .Returns((expectedJson, expectedDuration));
//
//         // Act
//         var (result, duration) = await _service.AnalyzeFrameWithTimingAsync(framePath);
//
//         // Assert
//         result.Should().NotBeNull();
//         result.MainDescription.Should().Be("一家日式拉麵店的外觀");
//         duration.Should().Be(expectedDuration);
//     }
//
//     [Fact]
//     public async Task AnalyzeFrameWithTimingAsync_WithYouTubeMetadata_ShouldIncludeMetadataInPrompt()
//     {
//         // Arrange
//         var framePath = "/test/frame.jpg";
//         var youtubeMetadata = MockData.CreateYouTubeVideoInfo();
//         var formattedMetadata = "=== YouTube 影片背景資訊 ===\n標題: 東京美食探索";
//         var expectedJson = MockData.CreateValidSceneDescriptionJson();
//         var expectedDuration = TimeSpan.FromMilliseconds(800);
//
//         _mockMetadataFormatter
//             .FormatMetadataForAI(youtubeMetadata)
//             .Returns(formattedMetadata);
//
//         _mockLMStudioClient
//             .SendVisionRequestAsync(framePath, Arg.Is<string>(prompt => prompt.Contains(formattedMetadata)), Arg.Any<CancellationToken>())
//             .Returns((expectedJson, expectedDuration));
//
//         // Act
//         var (result, duration) = await _service.AnalyzeFrameWithTimingAsync(framePath, youtubeMetadata);
//
//         // Assert
//         result.Should().NotBeNull();
//         duration.Should().Be(expectedDuration);
//
//         _mockMetadataFormatter.Received(1).FormatMetadataForAI(youtubeMetadata);
//         _mockLMStudioClient.Received(1)
//             .SendVisionRequestAsync(framePath, Arg.Is<string>(prompt => prompt.Contains(formattedMetadata)), Arg.Any<CancellationToken>());
//     }
//
//     [Fact]
//     public async Task AnalyzeFrameWithTimingAsync_WithNullYouTubeMetadata_ShouldCallBasicVersion()
//     {
//         // Arrange
//         var framePath = "/test/frame.jpg";
//         var expectedJson = MockData.CreateValidSceneDescriptionJson();
//         var expectedDuration = TimeSpan.FromMilliseconds(600);
//
//         _mockLMStudioClient
//             .SendVisionRequestAsync(framePath, Arg.Any<string>(), Arg.Any<CancellationToken>())
//             .Returns((expectedJson, expectedDuration));
//
//         // Act
//         var (result, duration) = await _service.AnalyzeFrameWithTimingAsync(framePath, (YouTubeVideoInfo?)null);
//
//         // Assert
//         result.Should().NotBeNull();
//         duration.Should().Be(expectedDuration);
//
//         _mockMetadataFormatter.DidNotReceive().FormatMetadataForAI(Arg.Any<YouTubeVideoInfo>());
//     }
//
//     #endregion
//
//     #region ExtractPlaceInfoAsync Tests
//
//     [Fact]
//     public async Task ExtractPlaceInfoAsync_WithValidImage_ShouldReturnPlaceInfos()
//     {
//         // Arrange
//         var framePath = "/test/frame.jpg";
//         var expectedJson = """
//         {
//             "placeInfos": [
//                 {
//                     "name": "一蘭拉麵 新宿店",
//                     "address": "東京都新宿區新宿3-34-11",
//                     "phone": "+81-3-1234-5678",
//                     "category": "拉麵店",
//                     "confidence": 0.85,
//                     "originalTexts": ["一蘭拉麵", "新宿店"]
//                 }
//             ]
//         }
//         """;
//
//         _mockLMStudioClient
//             .SendVisionRequestAsync(framePath, Arg.Any<string>(), Arg.Any<CancellationToken>())
//             .Returns((expectedJson, TimeSpan.FromMilliseconds(500)));
//
//         // Act
//         var result = await _service.ExtractPlaceInfoAsync(framePath);
//
//         // Assert
//         result.Should().HaveCount(1);
//         result[0].Name.Should().Be("一蘭拉麵 新宿店");
//         result[0].Address.Should().Be("東京都新宿區新宿3-34-11");
//         result[0].Confidence.Should().Be(0.85);
//     }
//
//     [Fact]
//     public async Task ExtractPlaceInfoAsync_WhenExceptionOccurs_ShouldReturnEmptyList()
//     {
//         // Arrange
//         var framePath = "/test/frame.jpg";
//         _mockLMStudioClient
//             .When(x => x.SendVisionRequestAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<CancellationToken>()))
//             .Do(x => throw new Exception("Network error"));
//
//         // Act
//         var result = await _service.ExtractPlaceInfoAsync(framePath);
//
//         // Assert
//         result.Should().BeEmpty();
//     }
//
//     #endregion
//
//     #region ExtractTextFromFrameAsync Tests
//
//     [Fact]
//     public async Task ExtractTextFromFrameAsync_WithValidImage_ShouldReturnDetectedTexts()
//     {
//         // Arrange
//         var framePath = "/test/frame.jpg";
//         var expectedJson = """
//         {
//             "detectedTexts": [
//                 {
//                     "text": "一蘭拉麵",
//                     "confidence": 0.9,
//                     "language": "ja",
//                     "location": "招牌"
//                 }
//             ]
//         }
//         """;
//
//         _mockLMStudioClient
//             .SendVisionRequestAsync(framePath, Arg.Any<string>(), Arg.Any<CancellationToken>())
//             .Returns((expectedJson, TimeSpan.FromMilliseconds(400)));
//
//         // Act
//         var result = await _service.ExtractTextFromFrameAsync(framePath);
//
//         // Assert
//         result.Should().HaveCount(1);
//         result[0].Text.Should().Be("一蘭拉麵");
//         result[0].Confidence.Should().Be(0.9);
//         result[0].Language.Should().Be("ja");
//         result[0].Location.Should().Be("招牌");
//     }
//
//     #endregion
//
//     #region AnalyzeSubtitleSegmentAsync Tests
//
//     [Fact]
//     public async Task AnalyzeSubtitleSegmentAsync_WithValidText_ShouldReturnAnalysis()
//     {
//         // Arrange
//         var text = "今天我們來到了東京最有名的拉麵店";
//         var expectedJson = """
//         {
//             "sentiment": "正面",
//             "topics": ["美食", "旅遊"],
//             "keywords": ["東京", "拉麵店"],
//             "isDialog": false,
//             "summary": "介紹東京拉麵店的旁白"
//         }
//         """;
//
//         _mockLMStudioClient
//             .SendTextRequestAsync(Arg.Any<string>(), Arg.Any<CancellationToken>())
//             .Returns(expectedJson);
//
//         // Act
//         var result = await _service.AnalyzeSubtitleSegmentAsync(text);
//
//         // Assert
//         result.Should().NotBeNull();
//         result.Sentiment.Should().Be("正面");
//         result.Topics.Should().Contain("美食");
//         result.Keywords.Should().Contain("東京");
//         result.IsDialog.Should().BeFalse();
//         result.Summary.Should().Be("介紹東京拉麵店的旁白");
//     }
//
//     #endregion
//
//     #region AnalyzeFramesBatchAsync Tests
//
//     [Fact]
//     public async Task AnalyzeFramesBatchAsync_WithMultipleFrames_ShouldReturnAllResults()
//     {
//         // Arrange
//         var framePaths = new[] { "/test/frame1.jpg", "/test/frame2.jpg", "/test/frame3.jpg" };
//         var expectedJson = MockData.CreateValidSceneDescriptionJson();
//
//         _mockLMStudioClient
//             .SendVisionRequestAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<CancellationToken>())
//             .Returns((expectedJson, TimeSpan.FromMilliseconds(500)));
//
//         // Act
//         var results = await _service.AnalyzeFramesBatchAsync(framePaths);
//
//         // Assert
//         results.Should().HaveCount(3);
//         results.Should().AllSatisfy(result => result.ShouldBeValidSceneDescription());
//
//         _mockLMStudioClient.Received(3)
//             .SendVisionRequestAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<CancellationToken>());
//     }
//
//     [Fact]
//     public async Task AnalyzeFramesBatchAsync_WithEmptyCollection_ShouldReturnEmptyList()
//     {
//         // Arrange
//         var framePaths = Array.Empty<string>();
//
//         // Act
//         var results = await _service.AnalyzeFramesBatchAsync(framePaths);
//
//         // Assert
//         results.Should().BeEmpty();
//         _mockLMStudioClient.DidNotReceive()
//             .SendVisionRequestAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<CancellationToken>());
//     }
//
//     [Fact]
//     public async Task AnalyzeFramesBatchAsync_WithSomeFailures_ShouldReturnPartialResults()
//     {
//         // Arrange
//         var framePaths = new[] { "/test/frame1.jpg", "/test/frame2.jpg" };
//         var expectedJson = MockData.CreateValidSceneDescriptionJson();
//
//         var callCount = 0;
//         _mockLMStudioClient
//             .SendVisionRequestAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<CancellationToken>())
//             .Returns(x =>
//             {
//                 callCount++;
//                 if (callCount == 1)
//                     return Task.FromResult((expectedJson, TimeSpan.FromMilliseconds(500)));
//                 else
//                     throw new HttpRequestException("Network error");
//             });
//
//         // Act
//         var results = await _service.AnalyzeFramesBatchAsync(framePaths);
//
//         // Assert
//         results.Should().HaveCount(2);
//         results[0].ShouldBeValidSceneDescription();
//         results[1].MainDescription.Should().Be("Analysis failed");
//     }
//
//     #endregion
//
//     #region GenerateVideoSummaryAsync Tests
//
//     [Fact]
//     public async Task GenerateVideoSummaryAsync_WithFrameAnalysesAndSubtitles_ShouldReturnSummary()
//     {
//         // Arrange
//         var frameAnalyses = new List<FrameAnalysis>
//         {
//             new FrameAnalysis
//             {
//                 Timestamp = TimeSpan.FromSeconds(10),
//                 FramePath = "/test/frame1.jpg",
//                 Scene = MockData.CreateSceneDescription(),
//                 PlaceInfos = new List<PlaceInfo> { MockData.CreatePlaceInfo() }
//             }
//         };
//
//         var subtitles = new List<SubtitleSegment>
//         {
//             MockData.CreateSubtitleSegment()
//         };
//
//         var expectedSummary = "這是一段關於東京拉麵店的影片摘要...";
//
//         _mockLMStudioClient
//             .SendTextRequestAsync(Arg.Any<string>(), Arg.Any<CancellationToken>())
//             .Returns(expectedSummary);
//
//         // Act
//         var (summaryText, subtitleContent, subtitleSegments) = await _service.GenerateVideoSummaryAsync(frameAnalyses, subtitles);
//
//         // Assert
//         summaryText.Should().Be(expectedSummary);
//         subtitleContent.Should().NotBeEmpty();
//         subtitleSegments.Should().NotBeEmpty();
//
//         _mockLMStudioClient.Received(1)
//             .SendTextRequestAsync(Arg.Is<string>(prompt => prompt.Contains("一蘭拉麵")), Arg.Any<CancellationToken>());
//     }
//
//     [Fact]
//     public async Task GenerateVideoSummaryAsync_WithYouTubeMetadata_ShouldIncludeMetadataInPrompt()
//     {
//         // Arrange
//         var frameAnalyses = new List<FrameAnalysis>();
//         var subtitles = new List<SubtitleSegment>();
//         var youtubeMetadata = MockData.CreateYouTubeVideoInfo();
//         var formattedMetadata = "=== YouTube 影片背景資訊 ===\n標題: 東京美食探索";
//         var expectedSummary = "基於 YouTube 元數據的影片摘要...";
//
//         _mockMetadataFormatter
//             .FormatMetadataForAI(youtubeMetadata)
//             .Returns(formattedMetadata);
//
//         _mockLMStudioClient
//             .SendTextRequestAsync(Arg.Is<string>(prompt => prompt.Contains(formattedMetadata)), Arg.Any<CancellationToken>())
//             .Returns(expectedSummary);
//
//         // Act
//         var (summaryText, _, _) = await _service.GenerateVideoSummaryAsync(frameAnalyses, subtitles, youtubeMetadata);
//
//         // Assert
//         summaryText.Should().Be(expectedSummary);
//         _mockMetadataFormatter.Received(1).FormatMetadataForAI(youtubeMetadata);
//     }
//
//     [Fact]
//     public async Task GenerateVideoSummaryAsync_WhenLMStudioFails_ShouldReturnErrorMessage()
//     {
//         // Arrange
//         var frameAnalyses = new List<FrameAnalysis>();
//         var subtitles = new List<SubtitleSegment>();
//
//         _mockLMStudioClient
//             .When(x => x.SendTextRequestAsync(Arg.Any<string>(), Arg.Any<CancellationToken>()))
//             .Do(x => throw new Exception("LM Studio connection failed"));
//
//         // Act
//         var (summaryText, _, _) = await _service.GenerateVideoSummaryAsync(frameAnalyses, subtitles);
//
//         // Assert
//         summaryText.Should().Be("Unable to generate video summary due to analysis error.");
//     }
//
//     #endregion
//
//     #region ConfirmRestaurantNamesAsync Tests
//
//     [Fact]
//     public async Task ConfirmRestaurantNamesAsync_WithValidNames_ShouldReturnConfirmedList()
//     {
//         // Arrange
//         var restaurantNames = new List<string> { "一蘭拉麵", "麥當勞", "星巴克" };
//         var expectedJson = """
//         {
//             "confirmed_restaurants": ["一蘭拉麵", "星巴克"]
//         }
//         """;
//
//         _mockLMStudioClient
//             .SendTextRequestAsync(Arg.Any<string>(), Arg.Any<CancellationToken>())
//             .Returns(expectedJson);
//
//         // Act
//         var result = await _service.ConfirmRestaurantNamesAsync(restaurantNames);
//
//         // Assert
//         result.Should().HaveCount(2);
//         result.Should().Contain("一蘭拉麵");
//         result.Should().Contain("星巴克");
//     }
//
//     [Fact]
//     public async Task ConfirmRestaurantNamesAsync_WithEmptyList_ShouldReturnEmptyList()
//     {
//         // Arrange
//         var restaurantNames = new List<string>();
//
//         // Act
//         var result = await _service.ConfirmRestaurantNamesAsync(restaurantNames);
//
//         // Assert
//         result.Should().BeEmpty();
//         _mockLMStudioClient.DidNotReceive()
//             .SendTextRequestAsync(Arg.Any<string>(), Arg.Any<CancellationToken>());
//     }
//
//     [Fact]
//     public async Task ConfirmRestaurantNamesAsync_WhenAIFails_ShouldReturnFilteredList()
//     {
//         // Arrange
//         // 使用 "営業中"（日文）而不是 "營業中"（繁體中文），因為 excludePatterns 中包含日文版本
//         var restaurantNames = new List<string> { "一蘭拉麵", "123", "営業中" };
//
//         _mockLMStudioClient
//             .When(x => x.SendTextRequestAsync(Arg.Any<string>(), Arg.Any<CancellationToken>()))
//             .Do(x => throw new Exception("AI service failed"));
//
//         // Act
//         var result = await _service.ConfirmRestaurantNamesAsync(restaurantNames);
//
//         // Assert
//         // 當 AI 失敗時，會使用 IsLikelyRestaurantName 方法進行基本過濾
//         // "一蘭拉麵" 包含 "麵" 關鍵字，應該被保留
//         // "営業中" 包含在 excludePatterns 中，應該被過濾掉
//         // "123" 是純數字，應該被過濾掉
//         result.Should().Contain("一蘭拉麵");
//         result.Should().NotContain("営業中");
//         result.Should().NotContain("123");
//     }
//
//     #endregion
//
//     #region ExtractRestaurantNamesFromReportAsync Tests
//
//     [Fact]
//     public async Task ExtractRestaurantNamesFromReportAsync_WithValidReport_ShouldReturnExtractedNames()
//     {
//         // Arrange
//         var reportText = "在這次的東京美食之旅中，我們造訪了一蘭拉麵、築地市場的壽司大，以及表參道的Bills鬆餅店。";
//         var expectedJson = """
//         {
//             "restaurant_names": ["一蘭拉麵", "壽司大", "Bills鬆餅店"]
//         }
//         """;
//
//         _mockOpenRouterClient
//             .SendTextRequestAsync(Arg.Any<string>(), Arg.Any<CancellationToken>())
//             .Returns(expectedJson);
//
//         // Act
//         var result = await _service.ExtractRestaurantNamesFromReportAsync(reportText);
//
//         // Assert
//         result.Should().HaveCount(3);
//         result.Should().Contain("一蘭拉麵");
//         result.Should().Contain("壽司大");
//         result.Should().Contain("Bills鬆餅店");
//     }
//
//     [Fact]
//     public async Task ExtractRestaurantNamesFromReportAsync_WithEmptyReport_ShouldReturnEmptyList()
//     {
//         // Arrange
//         var reportText = "";
//
//         // Act
//         var result = await _service.ExtractRestaurantNamesFromReportAsync(reportText);
//
//         // Assert
//         result.Should().BeEmpty();
//         _mockOpenRouterClient.DidNotReceive()
//             .SendTextRequestAsync(Arg.Any<string>(), Arg.Any<CancellationToken>());
//     }
//
//     #endregion
// }
