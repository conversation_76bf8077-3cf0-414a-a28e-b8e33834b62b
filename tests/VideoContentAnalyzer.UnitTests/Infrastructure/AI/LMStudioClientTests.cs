// using FluentAssertions;
// using Microsoft.Extensions.Logging;
// using Microsoft.Extensions.Options;
// using NSubstitute;
// using RestSharp;
// using System.Net;
// using System.Text.Json;
// using VideoContentAnalyzer.Infrastructure.AI;
// using VideoContentAnalyzer.Infrastructure.Http;
// using VideoContentAnalyzer.UnitTests.TestHelpers;
// using Xunit;
//
// namespace VideoContentAnalyzer.UnitTests.Infrastructure.AI;
//
// /// <summary>
// /// LMStudioClient 的整合測試
// /// 注意：由於 LMStudioClient 直接創建 RestClient，這些測試更像是整合測試
// /// 未來應該重構 LMStudioClient 以支持依賴注入來實現真正的單元測試
// /// </summary>
// public class LMStudioClientTests : IDisposable
// {
//     private readonly ILogger<LMStudioClient> _mockLogger;
//     private readonly IOptions<LMStudioOptions> _options;
//     private readonly IRestClientFactory _mockRestClientFactory;
//     private readonly LMStudioClient _client;
//
//     public LMStudioClientTests()
//     {
//         _mockLogger = TestFixtures.CreateMockLogger<LMStudioClient>();
//         _options = TestFixtures.CreateMockLMStudioOptions();
//         _mockRestClientFactory = TestFixtures.CreateMockRestClientFactory();
//
//         // 創建 LMStudioClient 實例，現在使用 Mock IRestClientFactory
//         _client = new LMStudioClient(_options, _mockLogger, _mockRestClientFactory);
//     }
//
//     #region SendVisionRequestAsync Tests
//
//     [Fact()]
//     public async Task SendVisionRequestAsync_WithValidImageAndPrompt_ShouldReturnResponse()
//     {
//         // Arrange
//         var imagePath = "/test/frame.jpg";
//         var prompt = "分析這張圖片";
//         var expectedResponse = """
//         {
//             "choices": [
//                 {
//                     "message": {
//                         "content": "這是一家日式拉麵店的外觀"
//                     }
//                 }
//             ]
//         }
//         """;
//
//         // 建立模擬的檔案系統
//         var fileSystem = TestFixtures.CreateMockFileSystem();
//
//         // 模擬成功的 HTTP 回應
//         var mockResponse = Substitute.For<RestResponse>();
//         mockResponse.IsSuccessful.Returns(true);
//         mockResponse.Content.Returns(expectedResponse);
//
//         // 注意：實際實作中需要重構 LMStudioClient 以支援依賴注入
//         // 這裡展示測試的結構和邏輯
//
//         // Act & Assert
//         // 由於當前的 LMStudioClient 實作不支援依賴注入 RestClient，
//         // 我們需要使用整合測試或重構類別來支援單元測試
//
//         // 暫時跳過實際執行，展示測試結構
//         await Task.CompletedTask;
//
//         // 預期的斷言：
//         // var (result, duration) = await _client.SendVisionRequestAsync(imagePath, prompt);
//         // result.Should().Contain("日式拉麵店");
//         // duration.Should().BePositive();
//     }
//
//     [Fact()]
//     public async Task SendVisionRequestAsync_WithInvalidImagePath_ShouldThrowFileNotFoundException()
//     {
//         // Arrange
//         var invalidImagePath = "/nonexistent/image.jpg";
//         var prompt = "分析這張圖片";
//
//         // Act & Assert
//         await Assert.ThrowsAsync<FileNotFoundException>(
//             () => _client.SendVisionRequestAsync(invalidImagePath, prompt));
//     }
//
//     [Fact()]
//     public async Task SendVisionRequestAsync_WithHttpError_ShouldThrowHttpRequestException()
//     {
//         // Arrange
//         var imagePath = "/test/frame.jpg";
//         var prompt = "分析這張圖片";
//
//         // 模擬 HTTP 錯誤回應
//         var mockResponse = Substitute.For<RestResponse>();
//         mockResponse.IsSuccessful.Returns(false);
//         mockResponse.StatusCode.Returns(HttpStatusCode.InternalServerError);
//         mockResponse.ErrorMessage.Returns("Internal Server Error");
//
//         // Act & Assert
//         // 實際測試需要重構 LMStudioClient 以支援依賴注入
//         await Task.CompletedTask;
//
//         // 預期的斷言：
//         // await Assert.ThrowsAsync<HttpRequestException>(
//         //     () => _client.SendVisionRequestAsync(imagePath, prompt));
//     }
//
//     [Fact()]
//     public async Task SendVisionRequestAsync_WithCancellationToken_ShouldRespectCancellation()
//     {
//         // Arrange
//         var imagePath = "/test/frame.jpg";
//         var prompt = "分析這張圖片";
//         var cancellationToken = TestFixtures.CreateCancelledToken();
//
//         // Act & Assert
//         await Assert.ThrowsAsync<OperationCanceledException>(
//             () => _client.SendVisionRequestAsync(imagePath, prompt, cancellationToken));
//     }
//
//     [Fact()]
//     public async Task SendVisionRequestAsync_WithTimeout_ShouldThrowTimeoutException()
//     {
//         // Arrange
//         var imagePath = "/test/frame.jpg";
//         var prompt = "分析這張圖片";
//         var shortTimeoutToken = TestFixtures.CreateTimeoutToken(100); // 100ms timeout
//
//         // 模擬長時間回應
//         var mockResponse = Substitute.For<RestResponse>();
//         mockResponse.IsSuccessful.Returns(true);
//         mockResponse.Content.Returns("{}");
//
//         // Act & Assert
//         // 實際測試需要模擬延遲回應
//         await Task.CompletedTask;
//
//         // 預期的斷言：
//         // await Assert.ThrowsAsync<OperationCanceledException>(
//         //     () => _client.SendVisionRequestAsync(imagePath, prompt, shortTimeoutToken));
//     }
//
//     [Fact()]
//     public async Task SendVisionRequestAsync_WithInvalidJsonResponse_ShouldThrowJsonException()
//     {
//         // Arrange
//         var imagePath = "/test/frame.jpg";
//         var prompt = "分析這張圖片";
//         var invalidJsonResponse = "{ invalid json }";
//
//         var mockResponse = Substitute.For<RestResponse>();
//         mockResponse.IsSuccessful.Returns(true);
//         mockResponse.Content.Returns(invalidJsonResponse);
//
//         // Act & Assert
//         // 實際測試需要重構 LMStudioClient
//         await Task.CompletedTask;
//
//         // 預期的斷言：
//         // await Assert.ThrowsAsync<JsonException>(
//         //     () => _client.SendVisionRequestAsync(imagePath, prompt));
//     }
//
//     #endregion
//
//     #region SendTextRequestAsync Tests
//
//     [Fact()]
//     public async Task SendTextRequestAsync_WithValidPrompt_ShouldReturnResponse()
//     {
//         // Arrange
//         var prompt = "請分析以下文字內容";
//         var expectedResponse = """
//         {
//             "choices": [
//                 {
//                     "message": {
//                         "content": "這是分析結果"
//                     }
//                 }
//             ]
//         }
//         """;
//
//         var mockResponse = Substitute.For<RestResponse>();
//         mockResponse.IsSuccessful.Returns(true);
//         mockResponse.Content.Returns(expectedResponse);
//
//         // Act & Assert
//         // 實際測試需要重構 LMStudioClient
//         await Task.CompletedTask;
//
//         // 預期的斷言：
//         // var result = await _client.SendTextRequestAsync(prompt);
//         // result.Should().Be("這是分析結果");
//     }
//
//     [Fact()]
//     public async Task SendTextRequestAsync_WithEmptyPrompt_ShouldHandleGracefully()
//     {
//         // Arrange
//         var emptyPrompt = "";
//
//         // Act & Assert
//         // 實際測試需要重構 LMStudioClient
//         await Task.CompletedTask;
//
//         // 預期的斷言：
//         // var result = await _client.SendTextRequestAsync(emptyPrompt);
//         // result.Should().NotBeNull();
//     }
//
//     [Fact()]
//     public async Task SendTextRequestAsync_WithHttpError_ShouldThrowHttpRequestException()
//     {
//         // Arrange
//         var prompt = "測試提示";
//
//         var mockResponse = Substitute.For<RestResponse>();
//         mockResponse.IsSuccessful.Returns(false);
//         mockResponse.StatusCode.Returns(HttpStatusCode.BadRequest);
//         mockResponse.ErrorMessage.Returns("Bad Request");
//
//         // Act & Assert
//         // 實際測試需要重構 LMStudioClient
//         await Task.CompletedTask;
//
//         // 預期的斷言：
//         // await Assert.ThrowsAsync<HttpRequestException>(
//         //     () => _client.SendTextRequestAsync(prompt));
//     }
//
//     [Fact()]
//     public async Task SendTextRequestAsync_WithCancellationToken_ShouldRespectCancellation()
//     {
//         // Arrange
//         var prompt = "測試提示";
//         var cancellationToken = TestFixtures.CreateCancelledToken();
//
//         // Act & Assert
//         await Assert.ThrowsAsync<OperationCanceledException>(
//             () => _client.SendTextRequestAsync(prompt, cancellationToken));
//     }
//
//     #endregion
//
//     #region ConvertImageToBase64Async Tests
//
//     [Fact()]
//     public async Task ConvertImageToBase64Async_WithValidImage_ShouldReturnBase64String()
//     {
//         // Arrange
//         var imagePath = "/test/frame.jpg";
//         var fileSystem = TestFixtures.CreateMockFileSystem();
//
//         // Act & Assert
//         // 實際測試需要重構 LMStudioClient 以支援檔案系統抽象
//         await Task.CompletedTask;
//
//         // 預期的斷言：
//         // var result = await _client.ConvertImageToBase64Async(imagePath);
//         // result.Should().NotBeNullOrEmpty();
//         // result.Should().MatchRegex("^[A-Za-z0-9+/]*={0,2}$"); // Base64 格式驗證
//     }
//
//     [Fact()]
//     public async Task ConvertImageToBase64Async_WithInvalidPath_ShouldThrowFileNotFoundException()
//     {
//         // Arrange
//         var invalidPath = "/nonexistent/image.jpg";
//
//         // Act & Assert
//         // TODO: ConvertImageToBase64Async 是私有方法，無法直接測試
//         // await Assert.ThrowsAsync<FileNotFoundException>(
//         //     () => _client.ConvertImageToBase64Async(invalidPath));
//         await Task.CompletedTask;
//     }
//
//     [Fact()]
//     public async Task ConvertImageToBase64Async_WithLargeImage_ShouldHandleEfficiently()
//     {
//         // Arrange
//         var imagePath = "/test/large_image.jpg";
//         // 建立大型圖片檔案的模擬
//
//         // Act & Assert
//         // 測試大型檔案的處理效率
//         await Task.CompletedTask;
//
//         // 預期的斷言：
//         // var stopwatch = Stopwatch.StartNew();
//         // var result = await _client.ConvertImageToBase64Async(imagePath);
//         // stopwatch.Stop();
//         //
//         // result.Should().NotBeNullOrEmpty();
//         // stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000); // 5秒內完成
//     }
//
//     #endregion
//
//     #region Dispose Tests
//
//     [Fact()]
//     public void Dispose_ShouldDisposeResourcesProperly()
//     {
//         // Arrange & Act
//         _client.Dispose();
//
//         // Assert
//         // 驗證資源已正確釋放
//         // 實際測試需要檢查 RestClient 是否已被釋放
//     }
//
//     [Fact()]
//     public void Dispose_CalledMultipleTimes_ShouldNotThrow()
//     {
//         // Arrange & Act & Assert
//         _client.Dispose();
//         _client.Dispose(); // 第二次呼叫不應拋出例外
//     }
//
//     #endregion
//
//     public void Dispose()
//     {
//         _client?.Dispose();
//     }
// }
