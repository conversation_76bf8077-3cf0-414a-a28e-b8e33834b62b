[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[ ] NAME:📋 重構計劃：讓 58 個跳過測試通過 DESCRIPTION:將直接使用文件系統和外部服務的類重構為支持依賴注入的架構，實現真正的單元測試。目標：讓所有 58 個跳過的測試通過。
--[/] NAME:🔧 階段 1：基礎設施準備 DESCRIPTION:準備重構所需的基礎設施，包括依賴注入配置和測試工具
---[x] NAME:1.1 安裝 System.IO.Abstractions 套件 DESCRIPTION:在 Infrastructure 和 UnitTests 項目中安裝 System.IO.Abstractions 和 TestingHelpers 套件。工作量：5 分鐘。風險：低
---[ ] NAME:1.2 更新依賴注入配置 DESCRIPTION:在 Program.cs 中註冊 IFileSystem 和 IRestClient 服務。工作量：10 分鐘。風險：低
---[ ] NAME:1.3 建立 IRestClient 接口 DESCRIPTION:為 RestClient 建立抽象接口，以支持更好的測試。工作量：15 分鐘。風險：中
---[ ] NAME:1.4 更新測試工具類 DESCRIPTION:更新 TestFixtures 類以支持 Mock IFileSystem 和 IRestClient。工作量：20 分鐘。風險：低
--[ ] NAME:📁 階段 2：文件系統依賴重構 (45 個測試) DESCRIPTION:重構 WhisperService, SubtitleService, VideoAnalyzer 以支持 IFileSystem 依賴注入
---[ ] NAME:2.1 重構 WhisperService (18 個測試) DESCRIPTION:重構 WhisperService 以使用 IFileSystem 依賴注入。工作量：45 分鐘。風險：中。優先級：高
----[ ] NAME:2.1.1 更新 WhisperService 構造函數 DESCRIPTION:在 WhisperService 構造函數中添加 IFileSystem 參數，更新所有直接文件系統調用
----[ ] NAME:2.1.2 更新 WhisperService 文件操作 DESCRIPTION:替換所有 File.Exists, File.OpenRead, Directory.CreateDirectory 等調用為 IFileSystem 版本
----[ ] NAME:2.1.3 更新 WhisperServiceTests DESCRIPTION:移除 Skip 屬性，使用 MockFileSystem 重寫測試，確保所有 18 個測試通過
---[ ] NAME:2.2 重構 SubtitleService (14 個測試) DESCRIPTION:重構 SubtitleService 以使用 IFileSystem 依賴注入。工作量：30 分鐘。風險：低。優先級：中
----[ ] NAME:2.2.1 更新 SubtitleService 構造函數 DESCRIPTION:在 SubtitleService 構造函數中添加 IFileSystem 參數
----[ ] NAME:2.2.2 更新 SubtitleService 文件操作 DESCRIPTION:替換 File.ReadAllLinesAsync, File.OpenRead 等調用為 IFileSystem 版本
----[ ] NAME:2.2.3 更新 SubtitleServiceTests DESCRIPTION:移除 Skip 屬性，使用 MockFileSystem 重寫測試，確保所有 14 個測試通過
---[ ] NAME:2.3 重構 VideoAnalyzer (13 個測試) DESCRIPTION:重構 VideoAnalyzer 以使用 IFileSystem 依賴注入。工作量：25 分鐘。風險：低。優先級：中
----[ ] NAME:2.3.1 更新 VideoAnalyzer 構造函數 DESCRIPTION:在 VideoAnalyzer 構造函數中添加 IFileSystem 參數
----[ ] NAME:2.3.2 更新 VideoAnalyzer 文件操作 DESCRIPTION:替換 File.Exists 調用為 IFileSystem 版本
----[ ] NAME:2.3.3 更新 VideoAnalyzerTests DESCRIPTION:移除 Skip 屬性，使用 MockFileSystem 重寫測試，確保所有 13 個測試通過
--[ ] NAME:🌐 階段 3：外部服務依賴重構 (13 個測試) DESCRIPTION:重構 LMStudioClient 以支持 RestClient 依賴注入
---[ ] NAME:3.1 建立 IRestClient 接口實現 DESCRIPTION:為 RestSharp 建立抽象接口和實現類，支持依賴注入。工作量：30 分鐘。風險：中
---[ ] NAME:3.2 重構 LMStudioClient (13 個測試) DESCRIPTION:重構 LMStudioClient 以使用 IRestClient 依賴注入。工作量：25 分鐘。風險：中
---[ ] NAME:3.3 更新 LMStudioClientTests DESCRIPTION:移除 Skip 屬性，使用 Mock IRestClient 重寫測試，確保所有 13 個測試通過
--[ ] NAME:✅ 階段 4：測試驗證與清理 DESCRIPTION:移除 Skip 屬性，運行測試，確保所有 58 個測試通過
---[ ] NAME:4.1 運行部分測試驗證 DESCRIPTION:分階段運行測試，驗證每個重構階段的成果。工作量：15 分鐘。風險：低
---[ ] NAME:4.2 運行完整測試套件 DESCRIPTION:運行所有 91 個測試，確保所有測試通過（目標：91 個成功，0 個失敗，0 個跳過）
---[ ] NAME:4.3 性能回歸測試 DESCRIPTION:確保重構後的代碼性能沒有明顯下降，驗證核心功能正常運作
---[ ] NAME:4.4 文檔更新 DESCRIPTION:更新 README 和相關文檔，記錄重構成果和新的依賴注入架構