#!/usr/bin/env python3
"""
Script to convert Moq syntax to NSubstitute syntax in C# test files
"""

import re
import os
import sys

def convert_moq_to_nsubstitute(content):
    """Convert Moq syntax to NSubstitute syntax"""

    # Replace using statements
    content = re.sub(r'using Moq;', 'using NSubstitute;', content)

    # Replace Mock<T> declarations
    content = re.sub(r'Mock<([^>]+)>', r'\1', content)

    # Replace new Mock<T>() with Substitute.For<T>()
    content = re.sub(r'new Mock<([^>]+)>\(\)', r'Substitute.For<\1>()', content)

    # Replace .Object property access
    content = re.sub(r'\.Object(?=[\s,\)])', '', content)

    # Replace It.IsAny<T>() with Arg.Any<T>()
    content = re.sub(r'It\.IsAny<([^>]+)>\(\)', r'Arg.Any<\1>()', content)

    # Replace It.Is<T>() with Arg.Is<T>()
    content = re.sub(r'It\.Is<([^>]+)>\(([^)]+)\)', r'Arg.Is<\1>(\2)', content)

    # Handle multi-line Setup patterns more carefully
    # Pattern 1: .Setup(x => x.Method(...)).ReturnsAsync(...)
    content = re.sub(
        r'\.Setup\(\s*([^)]+)\s*\)\s*\.ReturnsAsync\s*\(\s*([^)]+)\s*\)',
        r'.\1.Returns(\2)',
        content,
        flags=re.MULTILINE | re.DOTALL
    )

    # Pattern 2: .Setup(x => x.Method(...)).Returns(...)
    content = re.sub(
        r'\.Setup\(\s*([^)]+)\s*\)\s*\.Returns\s*\(\s*([^)]+)\s*\)',
        r'.\1.Returns(\2)',
        content,
        flags=re.MULTILINE | re.DOTALL
    )

    # Pattern 3: .Setup(x => x.Method(...)).ThrowsAsync(...)
    content = re.sub(
        r'\.Setup\(\s*([^)]+)\s*\)\s*\.ThrowsAsync\s*\(\s*([^)]+)\s*\)',
        r'.When(\1).Do(x => throw \2)',
        content,
        flags=re.MULTILINE | re.DOTALL
    )

    # Replace Verify with Received
    content = re.sub(r'\.Verify\(\s*([^,]+),\s*Times\.Once\s*\)', r'.Received(1).\1', content)
    content = re.sub(r'\.Verify\(\s*([^,]+),\s*Times\.Never\s*\)', r'.DidNotReceive().\1', content)
    content = re.sub(r'\.Verify\(\s*([^,]+),\s*Times\.Exactly\((\d+)\)\s*\)', r'.Received(\2).\1', content)
    content = re.sub(r'\.Verify\(\s*([^,]+),\s*Times\.AtLeastOnce\s*\)', r'.Received().\1', content)

    return content

def process_file(file_path):
    """Process a single file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        new_content = convert_moq_to_nsubstitute(content)
        
        if content != new_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            print(f"Updated: {file_path}")
        else:
            print(f"No changes needed: {file_path}")
            
    except Exception as e:
        print(f"Error processing {file_path}: {e}")

def main():
    if len(sys.argv) != 2:
        print("Usage: python update_moq_to_nsubstitute.py <directory>")
        sys.exit(1)
    
    directory = sys.argv[1]
    
    # Find all .cs files in the directory
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.cs'):
                file_path = os.path.join(root, file)
                process_file(file_path)

if __name__ == "__main__":
    main()
